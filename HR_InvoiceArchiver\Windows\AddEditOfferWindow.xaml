<Window x:Class="HR_InvoiceArchiver.Windows.AddEditOfferWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل عرض"
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   x:Name="TitleTextBlock"
                   Text="إضافة عرض جديد" 
                   Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- نموذج الإدخال -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- اسم المكتب العلمي -->
                <TextBox x:Name="ScientificOfficeTextBox"
                         materialDesign:HintAssist.Hint="اسم المكتب العلمي *"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- اسم المندوب -->
                <TextBox x:Name="RepresentativeNameTextBox"
                         materialDesign:HintAssist.Hint="اسم المندوب *"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- رقم المندوب -->
                <TextBox x:Name="RepresentativePhoneTextBox"
                         materialDesign:HintAssist.Hint="رقم المندوب"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- اسم المادة العلمية -->
                <ComboBox x:Name="ScientificNameComboBox"
                          materialDesign:HintAssist.Hint="اسم المادة العلمية *"
                          materialDesign:HintAssist.IsFloating="True"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          IsEditable="True"
                          FontSize="14"
                          Margin="0,0,0,15"/>

                <!-- اسم المادة التجارية -->
                <TextBox x:Name="TradeNameTextBox"
                         materialDesign:HintAssist.Hint="اسم المادة التجارية"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- السعر -->
                <TextBox x:Name="PriceTextBox"
                         materialDesign:HintAssist.Hint="السعر *"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- البونص أو الخصم -->
                <TextBox x:Name="BonusOrDiscountTextBox"
                         materialDesign:HintAssist.Hint="البونص أو الخصم"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Margin="0,0,0,15"/>

                <!-- الملاحظات -->
                <TextBox x:Name="NotesTextBox"
                         materialDesign:HintAssist.Hint="ملاحظات إضافية"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         FontSize="14"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,15"/>

                <!-- المرفقات -->
                <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="15"
                        Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="المرفقات"
                                   FontWeight="Medium"
                                   Margin="0,0,0,10"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock x:Name="AttachmentInfoTextBlock"
                                       Grid.Column="0"
                                       Text="لا يوجد مرفق"
                                       VerticalAlignment="Center"
                                       FontSize="12"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <Button x:Name="BrowseAttachmentButton"
                                    Grid.Column="1"
                                    Content="تصفح"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    materialDesign:ButtonAssist.CornerRadius="3"
                                    Height="32"
                                    Padding="15,0"
                                    Margin="10,0,0,0"
                                    Click="BrowseAttachmentButton_Click"/>

                            <Button x:Name="RemoveAttachmentButton"
                                    Grid.Column="2"
                                    Content="إزالة"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    materialDesign:ButtonAssist.CornerRadius="3"
                                    Height="32"
                                    Padding="15,0"
                                    Margin="5,0,0,0"
                                    Foreground="Red"
                                    BorderBrush="Red"
                                    Visibility="Collapsed"
                                    Click="RemoveAttachmentButton_Click"/>
                        </Grid>

                        <TextBlock Text="الملفات المدعومة: PDF, صور (JPG, PNG), Excel (XLS, XLSX)"
                                   FontSize="10"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار الإجراءات -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- رسالة التحقق -->
            <TextBlock x:Name="ValidationMessageTextBlock"
                       Grid.Column="0"
                       Foreground="Red"
                       FontSize="12"
                       VerticalAlignment="Center"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"/>

            <!-- زر الإلغاء -->
            <Button x:Name="CancelButton"
                    Grid.Column="1"
                    Content="إلغاء"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Width="80"
                    Margin="10,0,0,0"
                    Click="CancelButton_Click"/>

            <!-- زر الحفظ -->
            <Button x:Name="SaveButton"
                    Grid.Column="2"
                    Content="حفظ"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Width="80"
                    Margin="10,0,0,0"
                    Click="SaveButton_Click"/>
        </Grid>

        <!-- مؤشر التحميل -->
        <Grid x:Name="LoadingGrid"
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                             Width="50"
                             Height="50"
                             IsIndeterminate="True"
                             Foreground="White"/>
                <TextBlock Text="جاري الحفظ..."
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
