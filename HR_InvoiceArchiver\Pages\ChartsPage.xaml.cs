using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;

namespace HR_InvoiceArchiver.Pages
{
    public partial class ChartsPage : UserControl, INotifyPropertyChanged
    {
        private readonly IInvoiceService _invoiceService;
        private readonly IPaymentService _paymentService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private readonly IDashboardService _dashboardService;

        private string _currentChartType = "monthly";
        private bool _isLoading = false;

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
                UpdateLoadingVisibility();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ChartsPage(
            IInvoiceService invoiceService,
            IPaymentService paymentService,
            ISupplierService supplierService,
            IToastService toastService,
            IDashboardService dashboardService)
        {
            InitializeComponent();
            
            _invoiceService = invoiceService;
            _paymentService = paymentService;
            _supplierService = supplierService;
            _toastService = toastService;
            _dashboardService = dashboardService;

            DataContext = this;
            Loaded += ChartsPage_Loaded;
            Unloaded += ChartsPage_Unloaded;
        }

        private async void ChartsPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await Task.Delay(100);
                await LoadDefaultChart();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في التحميل", $"فشل في تحميل صفحة المخططات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ChartsPage_Loaded: {ex.Message}");
            }
        }

        private async Task LoadDefaultChart()
        {
            try
            {
                IsLoading = true;
                _currentChartType = "monthly";
                await ShowMonthlyTrends();
                UpdateButtonSelection("monthly");
                _toastService?.ShowInfo("تم التحميل", "تم تحميل المخططات البيانية بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل المخطط: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in LoadDefaultChart: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateLoadingVisibility()
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    if (LoadingIndicator != null)
                    {
                        LoadingIndicator.Visibility = IsLoading ? Visibility.Visible : Visibility.Collapsed;
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating loading visibility: {ex.Message}");
            }
        }

        #region Button Click Handlers

        private async void MonthlyTrendsButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "monthly";
                await ShowMonthlyTrends();
                UpdateButtonSelection("monthly");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل الاتجاهات الشهرية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in MonthlyTrendsButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void StatusDistributionButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "status";
                await ShowStatusDistribution();
                UpdateButtonSelection("status");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل توزيع الحالات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in StatusDistributionButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void SupplierAnalysisButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "supplier";
                await ShowSupplierAnalysis();
                UpdateButtonSelection("supplier");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تحليل الموردين: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in SupplierAnalysisButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void PaymentAnalysisButton_Click(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (IsLoading) return;
            
            try
            {
                IsLoading = true;
                _currentChartType = "payment";
                await ShowPaymentAnalysis();
                UpdateButtonSelection("payment");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل تحليل المدفوعات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in PaymentAnalysisButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Chart Display Methods

        private async Task ShowMonthlyTrends()
        {
            try
            {
                var monthlyTrends = await _dashboardService.GetMonthlyTrendsAsync(6);
                
                if (monthlyTrends == null || !monthlyTrends.Any())
                {
                    _toastService?.ShowWarning("لا توجد بيانات", "لا توجد بيانات اتجاهات شهرية لعرضها");
                    ShowErrorMessage("لا توجد بيانات للعرض");
                    return;
                }

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        CreateMonthlyTrendsDisplay(monthlyTrends);
                        System.Diagnostics.Debug.WriteLine($"Monthly trends displayed with {monthlyTrends.Count()} data points");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowMonthlyTrends UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });
                
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل الاتجاهات الشهرية بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل الاتجاهات الشهرية: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowMonthlyTrends: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowStatusDistribution()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                
                var unpaidCount = invoices.Count(i => i.Status == InvoiceStatus.Unpaid);
                var partiallyPaidCount = invoices.Count(i => i.Status == InvoiceStatus.PartiallyPaid);
                var paidCount = invoices.Count(i => i.Status == InvoiceStatus.Paid);

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        CreateStatusDistributionDisplay(unpaidCount, partiallyPaidCount, paidCount);
                        System.Diagnostics.Debug.WriteLine($"Status distribution displayed: Unpaid={unpaidCount}, Partial={partiallyPaidCount}, Paid={paidCount}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowStatusDistribution UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });
                
                _toastService?.ShowSuccess("تم التحديث", "تم تحميل توزيع الحالات بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحميل توزيع الحالات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowStatusDistribution: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowSupplierAnalysis()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();

                var supplierData = invoices
                    .GroupBy(i => i.Supplier.Name)
                    .Select(g => new
                    {
                        SupplierName = g.Key,
                        TotalAmount = g.Sum(i => i.Amount),
                        InvoiceCount = g.Count(),
                        PaidAmount = g.Sum(i => i.PaidAmount)
                    })
                    .OrderByDescending(s => s.TotalAmount)
                    .Take(10)
                    .ToList();

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        CreateSupplierAnalysisDisplay(supplierData);
                        System.Diagnostics.Debug.WriteLine($"Supplier analysis displayed with {supplierData.Count} suppliers");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowSupplierAnalysis UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });

                _toastService?.ShowSuccess("تم التحديث", "تم تحميل تحليل الموردين بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحليل الموردين: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowSupplierAnalysis: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        private async Task ShowPaymentAnalysis()
        {
            try
            {
                var payments = await _paymentService.GetAllPaymentsAsync();

                var monthlyPayments = payments
                    .GroupBy(p => new { p.PaymentDate.Year, p.PaymentDate.Month })
                    .Select(g => new
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        TotalAmount = g.Sum(p => p.Amount),
                        PaymentCount = g.Count()
                    })
                    .OrderBy(p => p.Year).ThenBy(p => p.Month)
                    .TakeLast(12)
                    .ToList();

                await Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        ShowChartData();
                        CreatePaymentAnalysisDisplay(monthlyPayments);
                        System.Diagnostics.Debug.WriteLine($"Payment analysis displayed with {monthlyPayments.Count} months");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error in ShowPaymentAnalysis UI update: {ex.Message}");
                        ShowErrorMessage("خطأ في عرض البيانات");
                    }
                });

                _toastService?.ShowSuccess("تم التحديث", "تم تحميل تحليل المدفوعات بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحليل المدفوعات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in ShowPaymentAnalysis: {ex.Message}");
                ShowErrorMessage("فشل في تحميل البيانات");
            }
        }

        #endregion

        #region Helper Methods

        private void ShowChartData()
        {
            if (DefaultMessage != null) DefaultMessage.Visibility = Visibility.Collapsed;
            if (ChartDataDisplay != null) ChartDataDisplay.Visibility = Visibility.Visible;
        }

        private void ShowErrorMessage(string message)
        {
            Dispatcher.Invoke(() =>
            {
                if (DefaultMessage != null) DefaultMessage.Visibility = Visibility.Visible;
                if (ChartDataDisplay != null) ChartDataDisplay.Visibility = Visibility.Collapsed;

                // Update default message to show error
                var messagePanel = DefaultMessage as StackPanel;
                if (messagePanel?.Children.Count > 1 && messagePanel.Children[1] is TextBlock textBlock)
                {
                    textBlock.Text = message;
                }
            });
        }

        private void CreateMonthlyTrendsDisplay(IEnumerable<MonthlyTrend> monthlyTrends)
        {
            if (ChartDataContainer == null) return;

            ChartDataContainer.Children.Clear();

            // Add title
            var titleBlock = new TextBlock
            {
                Text = "الاتجاهات الشهرية",
                Style = (Style)FindResource("MaterialDesignHeadline5TextBlock"),
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            ChartDataContainer.Children.Add(titleBlock);

            // Create data table
            var dataGrid = new Grid();
            dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Add headers
            AddTableHeader(dataGrid, 0, 0, "الشهر");
            AddTableHeader(dataGrid, 0, 1, "إجمالي الفواتير");
            AddTableHeader(dataGrid, 0, 2, "المبالغ المسددة");

            // Add data rows
            int rowIndex = 1;
            foreach (var trend in monthlyTrends)
            {
                dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                AddTableCell(dataGrid, rowIndex, 0, $"{trend.Month}/{trend.Year}");
                AddTableCell(dataGrid, rowIndex, 1, $"{trend.TotalAmount:N0} د.ع");
                AddTableCell(dataGrid, rowIndex, 2, $"{trend.PaidAmount:N0} د.ع");

                rowIndex++;
            }

            ChartDataContainer.Children.Add(dataGrid);
        }

        private void CreateStatusDistributionDisplay(int unpaidCount, int partiallyPaidCount, int paidCount)
        {
            if (ChartDataContainer == null) return;

            ChartDataContainer.Children.Clear();

            // Add title
            var titleBlock = new TextBlock
            {
                Text = "توزيع حالات الفواتير",
                Style = (Style)FindResource("MaterialDesignHeadline5TextBlock"),
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            ChartDataContainer.Children.Add(titleBlock);

            var totalCount = unpaidCount + partiallyPaidCount + paidCount;

            // Create status cards
            var statusPanel = new StackPanel();

            if (unpaidCount > 0)
            {
                var unpaidCard = CreateStatusCard("غير مسددة", unpaidCount, totalCount, "#F44336");
                statusPanel.Children.Add(unpaidCard);
            }

            if (partiallyPaidCount > 0)
            {
                var partialCard = CreateStatusCard("تسديد جزئي", partiallyPaidCount, totalCount, "#FF9800");
                statusPanel.Children.Add(partialCard);
            }

            if (paidCount > 0)
            {
                var paidCard = CreateStatusCard("مسددة", paidCount, totalCount, "#4CAF50");
                statusPanel.Children.Add(paidCard);
            }

            ChartDataContainer.Children.Add(statusPanel);
        }

        private void CreateSupplierAnalysisDisplay(dynamic supplierData)
        {
            if (ChartDataContainer == null) return;

            ChartDataContainer.Children.Clear();

            // Add title
            var titleBlock = new TextBlock
            {
                Text = "تحليل الموردين",
                Style = (Style)FindResource("MaterialDesignHeadline5TextBlock"),
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            ChartDataContainer.Children.Add(titleBlock);

            // Create data table
            var dataGrid = new Grid();
            dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Add headers
            AddTableHeader(dataGrid, 0, 0, "اسم المورد");
            AddTableHeader(dataGrid, 0, 1, "عدد الفواتير");
            AddTableHeader(dataGrid, 0, 2, "إجمالي المبلغ");
            AddTableHeader(dataGrid, 0, 3, "المبلغ المسدد");

            // Add data rows
            int rowIndex = 1;
            foreach (var supplier in supplierData)
            {
                dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                AddTableCell(dataGrid, rowIndex, 0, supplier.SupplierName);
                AddTableCell(dataGrid, rowIndex, 1, supplier.InvoiceCount.ToString());
                AddTableCell(dataGrid, rowIndex, 2, $"{supplier.TotalAmount:N0} د.ع");
                AddTableCell(dataGrid, rowIndex, 3, $"{supplier.PaidAmount:N0} د.ع");

                rowIndex++;
            }

            ChartDataContainer.Children.Add(dataGrid);
        }

        private void CreatePaymentAnalysisDisplay(dynamic monthlyPayments)
        {
            if (ChartDataContainer == null) return;

            ChartDataContainer.Children.Clear();

            // Add title
            var titleBlock = new TextBlock
            {
                Text = "تحليل المدفوعات الشهرية",
                Style = (Style)FindResource("MaterialDesignHeadline5TextBlock"),
                Margin = new Thickness(0, 0, 0, 16),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            ChartDataContainer.Children.Add(titleBlock);

            // Create data table
            var dataGrid = new Grid();
            dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            dataGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Add headers
            AddTableHeader(dataGrid, 0, 0, "الشهر");
            AddTableHeader(dataGrid, 0, 1, "عدد المدفوعات");
            AddTableHeader(dataGrid, 0, 2, "إجمالي المبلغ");

            // Add data rows
            int rowIndex = 1;
            foreach (var payment in monthlyPayments)
            {
                dataGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                AddTableCell(dataGrid, rowIndex, 0, $"{payment.Month}/{payment.Year}");
                AddTableCell(dataGrid, rowIndex, 1, payment.PaymentCount.ToString());
                AddTableCell(dataGrid, rowIndex, 2, $"{payment.TotalAmount:N0} د.ع");

                rowIndex++;
            }

            ChartDataContainer.Children.Add(dataGrid);
        }

        private Border CreateStatusCard(string title, int count, int total, string colorHex)
        {
            var percentage = total > 0 ? (double)count / total * 100 : 0;
            var color = (Color)ColorConverter.ConvertFromString(colorHex);

            var card = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(30, color.R, color.G, color.B)),
                BorderBrush = new SolidColorBrush(color),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(16),
                Margin = new Thickness(0, 0, 0, 12)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var titleBlock = new TextBlock
            {
                Text = title,
                FontWeight = FontWeights.Bold,
                FontSize = 16,
                Foreground = new SolidColorBrush(color)
            };
            Grid.SetColumn(titleBlock, 0);

            var countBlock = new TextBlock
            {
                Text = $"{count} ({percentage:F1}%)",
                FontWeight = FontWeights.Bold,
                FontSize = 18,
                Foreground = new SolidColorBrush(color)
            };
            Grid.SetColumn(countBlock, 1);

            grid.Children.Add(titleBlock);
            grid.Children.Add(countBlock);
            card.Child = grid;

            return card;
        }

        private void AddTableHeader(Grid grid, int row, int column, string text)
        {
            var header = new TextBlock
            {
                Text = text,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(8),
                Background = new SolidColorBrush(Color.FromArgb(50, 103, 58, 183)),
                Padding = new Thickness(8)
            };
            Grid.SetRow(header, row);
            Grid.SetColumn(header, column);
            grid.Children.Add(header);
        }

        private void AddTableCell(Grid grid, int row, int column, string text)
        {
            var cell = new TextBlock
            {
                Text = text,
                Margin = new Thickness(8),
                Padding = new Thickness(8)
            };
            Grid.SetRow(cell, row);
            Grid.SetColumn(cell, column);
            grid.Children.Add(cell);
        }

        private void UpdateButtonSelection(string selectedType)
        {
            // Update current chart type and implement button selection styling
            _currentChartType = selectedType;
            System.Diagnostics.Debug.WriteLine($"Chart type changed to: {_currentChartType}");

            // TODO: Add visual feedback for selected button when UI elements are available
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // Refresh current chart based on current chart type
            try
            {
                IsLoading = true;

                switch (_currentChartType)
                {
                    case "monthly":
                        await ShowMonthlyTrends();
                        break;
                    case "status":
                        await ShowStatusDistribution();
                        break;
                    case "supplier":
                        await ShowSupplierAnalysis();
                        break;
                    case "payment":
                        await ShowPaymentAnalysis();
                        break;
                    default:
                        await ShowMonthlyTrends();
                        break;
                }

                _toastService?.ShowSuccess("تم التحديث", "تم تحديث المخطط بنجاح");
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ", $"فشل في تحديث المخطط: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Error in RefreshButton_Click: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService?.ShowInfo("تصدير", "سيتم تنفيذ وظيفة التصدير قريباً");
        }

        private void ChartsPage_Unloaded(object sender, RoutedEventArgs e)
        {
            // Cleanup resources
        }

        #endregion
    }
}
