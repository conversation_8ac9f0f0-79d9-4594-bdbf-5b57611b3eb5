using System.IO;
using System.Windows;
using System.Windows.Controls;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Windows
{
    /// <summary>
    /// نافذة إضافة/تعديل العروض
    /// </summary>
    public partial class AddEditOfferWindow : Window
    {
        private readonly IOfferService _offerService;
        private readonly IToastService _toastService;
        private Offer? _currentOffer;
        private bool _isEditMode;
        private string? _selectedAttachmentPath;

        public bool IsSaved { get; private set; }

        public AddEditOfferWindow(Offer? offer = null)
        {
            InitializeComponent();
            
            // الحصول على الخدمات
            _offerService = App.ServiceProvider.GetRequiredService<IOfferService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            // تحديد وضع التحرير
            _currentOffer = offer;
            _isEditMode = offer != null;
            
            // تحديث العنوان
            TitleTextBlock.Text = _isEditMode ? "تعديل العرض" : "إضافة عرض جديد";
            SaveButton.Content = _isEditMode ? "تحديث" : "حفظ";
            
            // تحميل البيانات
            Loaded += AddEditOfferWindow_Loaded;
        }

        private async void AddEditOfferWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowLoading(true);
                
                // تحميل أسماء المواد العلمية
                await LoadScientificNamesAsync();
                
                // تحميل بيانات العرض في وضع التحرير
                if (_isEditMode && _currentOffer != null)
                {
                    LoadOfferData(_currentOffer);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في التحميل", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private async Task LoadScientificNamesAsync()
        {
            try
            {
                var scientificNames = await _offerService.GetAllScientificNamesAsync();
                ScientificNameComboBox.ItemsSource = scientificNames.Select(s => s.Name).ToList();
            }
            catch (Exception)
            {
                // في حالة الخطأ، اترك القائمة فارغة
                ScientificNameComboBox.ItemsSource = new List<string>();
            }
        }

        private void LoadOfferData(Offer offer)
        {
            ScientificOfficeTextBox.Text = offer.ScientificOffice;
            RepresentativeNameTextBox.Text = offer.RepresentativeName;
            RepresentativePhoneTextBox.Text = offer.RepresentativePhone;
            ScientificNameComboBox.Text = offer.ScientificName;
            TradeNameTextBox.Text = offer.TradeName;
            PriceTextBox.Text = offer.Price.ToString();
            BonusOrDiscountTextBox.Text = offer.BonusOrDiscount;
            NotesTextBox.Text = offer.Notes;

            // تحميل معلومات المرفق
            if (offer.HasAttachment)
            {
                _selectedAttachmentPath = offer.AttachmentPath;
                UpdateAttachmentDisplay("مرفق موجود", null);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                ShowLoading(true);

                // إنشاء أو تحديث العرض
                var offer = await CreateOfferFromInputAsync();
                
                if (_isEditMode)
                {
                    offer.Id = _currentOffer!.Id;
                    await _offerService.UpdateOfferAsync(offer);
                    _toastService?.ShowSuccess("تم التحديث", "تم تحديث العرض بنجاح");
                }
                else
                {
                    await _offerService.CreateOfferAsync(offer);
                    _toastService?.ShowSuccess("تم الحفظ", "تم إضافة العرض بنجاح");
                }

                IsSaved = true;
                Close();
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في الحفظ", ex.Message);
            }
            finally
            {
                ShowLoading(false);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private bool ValidateInput()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(ScientificOfficeTextBox.Text))
                errors.Add("اسم المكتب العلمي مطلوب");

            if (string.IsNullOrWhiteSpace(RepresentativeNameTextBox.Text))
                errors.Add("اسم المندوب مطلوب");

            if (string.IsNullOrWhiteSpace(ScientificNameComboBox.Text))
                errors.Add("اسم المادة العلمية مطلوب");

            if (string.IsNullOrWhiteSpace(PriceTextBox.Text))
                errors.Add("السعر مطلوب");
            else if (!decimal.TryParse(PriceTextBox.Text, out decimal price) || price <= 0)
                errors.Add("السعر يجب أن يكون رقماً صحيحاً أكبر من صفر");

            if (errors.Any())
            {
                ValidationMessageTextBlock.Text = string.Join("\n", errors);
                ValidationMessageTextBlock.Visibility = Visibility.Visible;
                return false;
            }

            ValidationMessageTextBlock.Visibility = Visibility.Collapsed;
            return true;
        }

        private async Task<Offer> CreateOfferFromInputAsync()
        {
            var offer = new Offer
            {
                ScientificOffice = ScientificOfficeTextBox.Text.Trim(),
                RepresentativeName = RepresentativeNameTextBox.Text.Trim(),
                RepresentativePhone = RepresentativePhoneTextBox.Text?.Trim(),
                ScientificName = ScientificNameComboBox.Text.Trim(),
                TradeName = TradeNameTextBox.Text?.Trim(),
                Price = decimal.Parse(PriceTextBox.Text),
                BonusOrDiscount = BonusOrDiscountTextBox.Text?.Trim(),
                Notes = NotesTextBox.Text?.Trim(),
                CreatedAt = _isEditMode ? _currentOffer!.CreatedAt : DateTime.Now,
                UpdatedAt = _isEditMode ? DateTime.Now : null,
                IsActive = true
            };

            // معالجة المرفق
            if (!string.IsNullOrEmpty(_selectedAttachmentPath))
            {
                await ProcessAttachmentAsync(offer);
            }

            return offer;
        }

        private async Task ProcessAttachmentAsync(Offer offer)
        {
            try
            {
                if (!File.Exists(_selectedAttachmentPath))
                    return;

                // إنشاء مجلد المرفقات إذا لم يكن موجوداً
                var attachmentsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments", "Offers");
                Directory.CreateDirectory(attachmentsDir);

                // إنشاء اسم ملف فريد
                var fileName = Path.GetFileName(_selectedAttachmentPath);
                var fileExtension = Path.GetExtension(fileName);
                var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
                var destinationPath = Path.Combine(attachmentsDir, uniqueFileName);

                // نسخ الملف
                await Task.Run(() => File.Copy(_selectedAttachmentPath, destinationPath, true));

                // تحديث معلومات المرفق في العرض
                offer.AttachmentPath = destinationPath;
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في معالجة المرفق", ex.Message);
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingGrid.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            SaveButton.IsEnabled = !isLoading;
            CancelButton.IsEnabled = !isLoading;
        }

        // وظائف المرفقات
        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختيار مرفق",
                    Filter = "جميع الملفات المدعومة|*.pdf;*.jpg;*.jpeg;*.png;*.xls;*.xlsx|" +
                            "ملفات PDF|*.pdf|" +
                            "الصور|*.jpg;*.jpeg;*.png|" +
                            "ملفات Excel|*.xls;*.xlsx|" +
                            "جميع الملفات|*.*",
                    FilterIndex = 1,
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    _selectedAttachmentPath = openFileDialog.FileName;
                    var fileInfo = new FileInfo(_selectedAttachmentPath);
                    UpdateAttachmentDisplay(fileInfo.Name, fileInfo.Length);
                }
            }
            catch (Exception ex)
            {
                _toastService?.ShowError("خطأ في اختيار الملف", ex.Message);
            }
        }

        private void RemoveAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            _selectedAttachmentPath = null;
            UpdateAttachmentDisplay(null, null);
        }

        private void UpdateAttachmentDisplay(string? fileName, long? fileSize)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                AttachmentInfoTextBlock.Text = "لا يوجد مرفق";
                RemoveAttachmentButton.Visibility = Visibility.Collapsed;
            }
            else
            {
                var sizeText = fileSize.HasValue ? $" ({FormatFileSize(fileSize.Value)})" : "";
                AttachmentInfoTextBlock.Text = $"{fileName}{sizeText}";
                RemoveAttachmentButton.Visibility = Visibility.Visible;
            }
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
