<Window x:Class="HR_InvoiceArchiver.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Invoice Archiver"
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- Modern Flat Colors -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#1E40AF"/>
        <SolidColorBrush x:Key="AccentColor" Color="#10B981"/>
        <SolidColorBrush x:Key="ErrorColor" Color="#EF4444"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8FAFC"/>
        <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

        <!-- Modern Blue Gradient for Sidebar -->
        <LinearGradientBrush x:Key="SidebarGradient" StartPoint="0,0" EndPoint="0,1">
            <GradientStop Color="#1E40AF" Offset="0"/>
            <GradientStop Color="#3B82F6" Offset="0.5"/>
            <GradientStop Color="#60A5FA" Offset="1"/>
        </LinearGradientBrush>

        <!-- Selected Button Gradient -->
        <LinearGradientBrush x:Key="SelectedButtonGradient" StartPoint="0,0" EndPoint="1,0">
            <GradientStop Color="#10B981" Offset="0"/>
            <GradientStop Color="#059669" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Navigation Button Style -->
        <Style x:Key="ModernNavButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="55"/>
            <Setter Property="Margin" Value="8,3"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Margin="{TemplateBinding Margin}"
                                Padding="20,0">
                            <ContentPresenter HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1AFFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Selected Button Style -->
        <Style x:Key="SelectedNavButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernNavButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SelectedButtonGradient}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="12"
                                Margin="{TemplateBinding Margin}"
                                Padding="20,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#10B981"
                                                  Opacity="0.4"
                                                  BlurRadius="12"
                                                  ShadowDepth="3"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Window Control Button Style for Sidebar -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="46"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1AFFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Close Button Style for Sidebar -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource ErrorColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Window Border with Modern Shadow -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="16">
        <Border.Effect>
            <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="25" ShadowDepth="5"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Invisible Header for Window Dragging -->
            <Border Grid.Row="0" Grid.Column="1"
                    Background="Transparent"
                    Height="30"
                    VerticalAlignment="Top"
                    MouseLeftButtonDown="HeaderBar_MouseLeftButtonDown">
            </Border>

            <!-- Extended Sidebar with Window Controls -->
            <Border Grid.Row="0" Grid.Column="0"
                    Background="{StaticResource SidebarGradient}"
                    CornerRadius="16,0,0,16">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="12" ShadowDepth="3" Direction="0"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="60"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Window Control Buttons in Sidebar Header -->
                    <Border Grid.Row="0" Background="Transparent" CornerRadius="16,0,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="15,0,0,0">
                            <Button x:Name="CloseButton" Style="{StaticResource CloseButtonStyle}" Click="CloseButton_Click">
                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Foreground="White"/>
                            </Button>
                            <Button x:Name="MaximizeButton" Style="{StaticResource WindowControlButtonStyle}" Click="MaximizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16" Foreground="White"/>
                            </Button>
                            <Button x:Name="MinimizeButton" Style="{StaticResource WindowControlButtonStyle}" Click="MinimizeButton_Click">
                                <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16" Foreground="White"/>
                            </Button>
                        </StackPanel>
                    </Border>

                    <!-- Sidebar Content -->
                    <StackPanel Grid.Row="1" Margin="0,10,0,20">
                        <!-- Application Title - Flat Design -->
                        <Border Background="Transparent"
                                Margin="20,0,20,25"
                                Padding="0,15,0,20">
                            <StackPanel HorizontalAlignment="Right">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,8">
                                    <TextBlock Text="نظام أرشفة الفواتير"
                                               FontSize="20"
                                               FontWeight="Bold"
                                               FontFamily="Segoe UI"
                                               Foreground="White"
                                               VerticalAlignment="Center"
                                               Margin="0,0,12,0"/>
                                    <materialDesign:PackIcon Kind="FileDocumentOutline"
                                                             Width="24" Height="24"
                                                             VerticalAlignment="Center"
                                                             Foreground="White"/>
                                </StackPanel>
                                <Rectangle Height="2"
                                           Fill="White"
                                           Opacity="0.3"
                                           HorizontalAlignment="Stretch"
                                           Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Main Tools Section Header -->
                        <Border Background="#26FFFFFF"
                                CornerRadius="12"
                                Margin="15,0,15,15"
                                Padding="15,10">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                         Width="16" Height="16"
                                                         Foreground="White"
                                                         Margin="0,0,8,0"/>
                                <TextBlock Text="الأدوات الرئيسية"
                                           FontSize="13"
                                           FontWeight="SemiBold"
                                           Foreground="White"/>
                            </StackPanel>
                        </Border>

                        <!-- Main Navigation Buttons -->
                        <Button x:Name="DashboardButton"
                                Style="{StaticResource SelectedNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="لوحة التحكم" Foreground="White" FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InvoicesButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الفواتير" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="FileDocumentOutline" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="PaymentsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="المدفوعات" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="CreditCard" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SuppliersButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الموردين" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="OffersButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="عروض المندوبين" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SearchButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="البحث" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ReportsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="التقارير" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="FileChart" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ChartsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="المخططات البيانية" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Separator -->
                        <Border Height="1" Background="#33FFFFFF" Margin="25,20,25,20"/>

                        <!-- Advanced Tools Section Header -->
                        <Border Background="#26FFFFFF"
                                CornerRadius="12"
                                Margin="15,0,15,15"
                                Padding="15,10">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <materialDesign:PackIcon Kind="Cog"
                                                         Width="16" Height="16"
                                                         Foreground="White"
                                                         Margin="0,0,8,0"/>
                                <TextBlock Text="الأدوات المتقدمة"
                                           FontSize="13"
                                           FontWeight="SemiBold"
                                           Foreground="White"/>
                            </StackPanel>
                        </Border>

                        <Button x:Name="SettingsButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الإعدادات" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Cog" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ImportExportButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="الاستيراد والتصدير" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Import" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="BackupRestoreButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="النسخ الاحتياطي" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="PerformanceButton"
                                Style="{StaticResource ModernNavButtonStyle}"
                                Click="NavigationButton_Click">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <TextBlock Text="تحسين الأداء" Foreground="White" FontSize="14" FontWeight="Medium" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                <materialDesign:PackIcon Kind="Flash" Width="20" Height="20" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Separator -->
                        <Border Height="1" Background="#33FFFFFF" Margin="25,25,25,20"/>

                        <!-- Cloud Storage Section - Flat Design -->
                        <Border Background="Transparent"
                                Margin="15,0,15,20"
                                Padding="0">
                            <Button x:Name="CloudStorageButton"
                                    Height="60"
                                    Background="#26FFFFFF"
                                    BorderThickness="0"
                                    Cursor="Hand"
                                    Click="CloudStorageButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border x:Name="border"
                                                            Background="{TemplateBinding Background}"
                                                            CornerRadius="12"
                                                            Padding="20,0">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter TargetName="border" Property="Background" Value="#40FFFFFF"/>
                                                        </Trigger>
                                                        <Trigger Property="IsPressed" Value="True">
                                                            <Setter TargetName="border" Property="Background" Value="#1AFFFFFF"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CloudUpload"
                                                             Width="28" Height="28"
                                                             Foreground="White"
                                                             HorizontalAlignment="Center"
                                                             Margin="0,0,0,8"/>
                                    <TextBlock Text="التخزين السحابي"
                                               FontSize="13"
                                               FontWeight="Medium"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               TextAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Main Content Area - Full Flat -->
            <Border Grid.Row="0" Grid.Column="1"
                    Background="{StaticResource BackgroundColor}"
                    CornerRadius="0,16,16,0"
                    Margin="8,0,0,0">
                <ContentPresenter x:Name="MainContentPresenter" Margin="0,0,0,0"/>
            </Border>
        </Grid>
    </Border>
</Window>
