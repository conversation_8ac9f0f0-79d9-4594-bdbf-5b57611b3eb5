<Application x:Class="HR_InvoiceArchiver.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:HR_InvoiceArchiver"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Modern Material Design Theme with Blue Primary -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Green" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />

                <!-- Custom Modern Styles -->
                <ResourceDictionary Source="pack://application:,,,/HR_InvoiceArchiver;component/Resources/ModernStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- إضافة المحولات المطلوبة -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- محولات إضافية للتأكد من عدم وجود أخطاء -->
            <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>
