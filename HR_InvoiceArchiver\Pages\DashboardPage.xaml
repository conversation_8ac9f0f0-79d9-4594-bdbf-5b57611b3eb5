<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Pages.DashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             Background="#F8F9FF">

    <UserControl.Resources>
        <ResourceDictionary>

            <!-- Modern Gradient Brushes - Updated Colors -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#10B981" Offset="0"/>
                <GradientStop Color="#059669" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF6B6B" Offset="0"/>
                <GradientStop Color="#EE5A52" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="ErrorGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#8B5CF6" Offset="0"/>
                <GradientStop Color="#7C3AED" Offset="1"/>
            </LinearGradientBrush>

            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#60000000" BlurRadius="25" ShadowDepth="10" Direction="270"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Animated Statistics Card Style -->
            <Style x:Key="StatCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCardStyle}">
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.05" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.05" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.2"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Modern Button Style -->
            <Style x:Key="ModernActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="90"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="20"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Trigger.EnterActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1.03" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1.03" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.EnterActions>
                        <Trigger.ExitActions>
                            <BeginStoryboard>
                                <Storyboard>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                   To="1" Duration="0:0:0.15"/>
                                    <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                   To="1" Duration="0:0:0.15"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </Trigger.ExitActions>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Loading Animation -->
            <Storyboard x:Key="LoadingAnimation" RepeatBehavior="Forever">
                <DoubleAnimation Storyboard.TargetName="LoadingIcon"
                               Storyboard.TargetProperty="RenderTransform.Angle"
                               From="0" To="360" Duration="0:0:2"/>
            </Storyboard>

            <!-- Fade In Animation -->
            <Storyboard x:Key="FadeInAnimation">
                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                               From="0" To="1" Duration="0:0:0.5"/>
                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                               From="20" To="0" Duration="0:0:0.5"/>
            </Storyboard>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container Grid -->
    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="48" Height="48"
                                       Foreground="{StaticResource PrimaryGradientBrush}">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل البيانات..." Margin="0,16,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="24" x:Name="MainContent">
            <Grid.RenderTransform>
                <TranslateTransform Y="0"/>
            </Grid.RenderTransform>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Modern Welcome Header -->
            <Border Background="#667eea" CornerRadius="16" Margin="0,0,0,24">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
                </Border.Effect>

                <Grid Margin="32,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Border Grid.Column="0" Background="#FFFFFF20" CornerRadius="32" Width="64" Height="64"
                            VerticalAlignment="Center" Margin="0,0,24,0">
                        <materialDesign:PackIcon Kind="ViewDashboard" Width="32" Height="32"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="مرحباً بك في نظام أرشيف الفواتير المتطور"
                                 FontSize="28" FontWeight="Bold" Foreground="White"/>
                        <TextBlock x:Name="WelcomeSubtitle" Text="إدارة شاملة وذكية لفواتيرك ومدفوعاتك مع تحليلات متقدمة"
                                 FontSize="16" Foreground="#E0E7FF" Margin="0,8,0,0"/>
                        <TextBlock x:Name="LastUpdateText" Text="آخر تحديث: الآن"
                                 FontSize="14" Foreground="#E0E7FF" Opacity="0.8" Margin="0,4,0,0"/>
                    </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="تحديث البيانات" Click="RefreshButton_Click"
                                  Width="48" Height="48" Margin="0,0,8,0">
                                <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                            </Button>
                            <Button x:Name="SettingsQuickButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="الإعدادات السريعة" Click="SettingsButton_Click"
                                  Width="48" Height="48">
                                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                            </Button>
                        </StackPanel>
                    </Grid>
            </Border>

            <!-- Enhanced Quick Stats Summary -->
            <Grid Grid.Row="1" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Invoices Quick Card -->
                <Border Grid.Column="0" Background="White" CornerRadius="16" Margin="0,0,8,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#667eea" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickTotalInvoices" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي الفواتير" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#F0F4FF" CornerRadius="8" Padding="8,4" Margin="0,12,0,0">
                            <TextBlock Text="جميع الفواتير" FontSize="12" Foreground="#667eea"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Total Amount Quick Card -->
                <Border Grid.Column="1" Background="White" CornerRadius="16" Margin="4,0,4,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#10B981" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#10B981" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickTotalAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="إجمالي المبلغ" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#F0FDF4" CornerRadius="8" Padding="8,4" Margin="0,12,0,0">
                            <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#10B981"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Paid Amount Quick Card -->
                <Border Grid.Column="2" Background="White" CornerRadius="16" Margin="4,0,4,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#FF6B6B" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#FF6B6B" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickPaidAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="المبلغ المسدد" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#FFF5F5" CornerRadius="8" Padding="8,4" Margin="0,12,0,0">
                            <TextBlock Text="مدفوع" FontSize="12" Foreground="#FF6B6B"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Outstanding Amount Quick Card -->
                <Border Grid.Column="3" Background="White" CornerRadius="16" Margin="8,0,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#8B5CF6" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Border.Effect>
                    <Grid Margin="24,20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#8B5CF6" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1" HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickOutstandingAmount" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="المبلغ المتبقي" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#FAF5FF" CornerRadius="8" Padding="8,4" Margin="0,12,0,0">
                            <TextBlock Text="متبقي" FontSize="12" Foreground="#8B5CF6"
                                     FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>
            </Grid>

            <!-- Enhanced 3D Action Cards -->
            <Grid Grid.Row="2" Margin="0,0,0,32">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Quick Actions 3D Card -->
                <Border Grid.Column="0" Background="White" CornerRadius="20" Margin="0,0,16,0" Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="#667eea" Opacity="0.2" BlurRadius="25" ShadowDepth="10"/>
                    </Border.Effect>
                    <Border.RenderTransform>
                        <TranslateTransform Y="0"/>
                    </Border.RenderTransform>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="-8" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="15" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="0" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="10" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>

                    <Grid Margin="24,32">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 3D Icon Container -->
                        <Border Grid.Row="0" Background="#667eea" CornerRadius="50" Width="80" Height="80"
                                HorizontalAlignment="Center" Margin="0,0,0,20">
                            <Border.Effect>
                                <DropShadowEffect Color="#667eea" Opacity="0.4" BlurRadius="15" ShadowDepth="5"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="Flash" Width="40" Height="40" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <TextBlock Grid.Row="1" Text="الإجراءات السريعة" FontSize="20" FontWeight="Bold"
                                 Foreground="#2D3748" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                        <TextBlock Grid.Row="2" Text="الوصول السريع للمهام الأساسية" FontSize="14"
                                 Foreground="#718096" HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>

                        <StackPanel Grid.Row="3">
                            <Button Content="إضافة فاتورة جديدة" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#667eea" Foreground="#667eea" Height="36" FontSize="12" Margin="0,0,0,8"/>
                            <Button Content="عرض التقارير" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#667eea" Foreground="#667eea" Height="36" FontSize="12"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Analytics 3D Card -->
                <Border Grid.Column="1" Background="White" CornerRadius="20" Margin="8,0,8,0" Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="#10B981" Opacity="0.2" BlurRadius="25" ShadowDepth="10"/>
                    </Border.Effect>
                    <Border.RenderTransform>
                        <TranslateTransform Y="0"/>
                    </Border.RenderTransform>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="-8" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="15" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="0" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="10" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>

                    <Grid Margin="24,32">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 3D Icon Container -->
                        <Border Grid.Row="0" Background="#10B981" CornerRadius="50" Width="80" Height="80"
                                HorizontalAlignment="Center" Margin="0,0,0,20">
                            <Border.Effect>
                                <DropShadowEffect Color="#10B981" Opacity="0.4" BlurRadius="15" ShadowDepth="5"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="ChartLine" Width="40" Height="40" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <TextBlock Grid.Row="1" Text="التحليلات المتقدمة" FontSize="20" FontWeight="Bold"
                                 Foreground="#2D3748" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                        <TextBlock Grid.Row="2" Text="رؤى ذكية وتحليلات شاملة للبيانات" FontSize="14"
                                 Foreground="#718096" HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>

                        <StackPanel Grid.Row="3">
                            <Button Content="المخططات البيانية" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#10B981" Foreground="#10B981" Height="36" FontSize="12" Margin="0,0,0,8"/>
                            <Button Content="التقارير التفصيلية" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#10B981" Foreground="#10B981" Height="36" FontSize="12"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Management 3D Card -->
                <Border Grid.Column="2" Background="White" CornerRadius="20" Margin="16,0,0,0" Cursor="Hand">
                    <Border.Effect>
                        <DropShadowEffect Color="#8B5CF6" Opacity="0.2" BlurRadius="25" ShadowDepth="10"/>
                    </Border.Effect>
                    <Border.RenderTransform>
                        <TranslateTransform Y="0"/>
                    </Border.RenderTransform>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Trigger.EnterActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="-8" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="15" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.EnterActions>
                                    <Trigger.ExitActions>
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Y"
                                                               To="0" Duration="0:0:0.3"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Effect.ShadowDepth"
                                                               To="10" Duration="0:0:0.3"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </Trigger.ExitActions>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>

                    <Grid Margin="24,32">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 3D Icon Container -->
                        <Border Grid.Row="0" Background="#8B5CF6" CornerRadius="50" Width="80" Height="80"
                                HorizontalAlignment="Center" Margin="0,0,0,20">
                            <Border.Effect>
                                <DropShadowEffect Color="#8B5CF6" Opacity="0.4" BlurRadius="15" ShadowDepth="5"/>
                            </Border.Effect>
                            <materialDesign:PackIcon Kind="Settings" Width="40" Height="40" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <TextBlock Grid.Row="1" Text="إدارة النظام" FontSize="20" FontWeight="Bold"
                                 Foreground="#2D3748" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                        <TextBlock Grid.Row="2" Text="إعدادات وإدارة شاملة للنظام" FontSize="14"
                                 Foreground="#718096" HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>

                        <StackPanel Grid.Row="3">
                            <Button Content="إدارة الموردين" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#8B5CF6" Foreground="#8B5CF6" Height="36" FontSize="12" Margin="0,0,0,8"/>
                            <Button Content="إعدادات النظام" Style="{StaticResource MaterialDesignOutlinedButton}"
                                    BorderBrush="#8B5CF6" Foreground="#8B5CF6" Height="36" FontSize="12"/>
                        </StackPanel>
                    </Grid>
                </Border>

            </Grid>



            <!-- Recent Activities and Alerts -->
            <Grid Grid.Row="3" Margin="0,0,0,24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Recent Invoices -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="350"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="الفواتير الحديثة"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="آخر الفواتير المضافة إلى النظام"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="FilterRecentButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="فلترة الفواتير" Click="FilterRecentButton_Click">
                                    <materialDesign:PackIcon Kind="Filter" Width="18" Height="18"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignFlatButton}"
                                      Content="عرض الكل" Click="ViewAllInvoicesButton_Click" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Grid>

                        <!-- Filter Options -->
                        <Grid Grid.Row="1" x:Name="RecentInvoicesFilterPanel" Visibility="Collapsed" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="الحالة:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox Grid.Column="1" x:Name="RecentStatusFilterComboBox" Width="120"
                                    SelectionChanged="RecentStatusFilter_Changed">
                                <ComboBoxItem Content="الكل" IsSelected="True"/>
                                <ComboBoxItem Content="غير مسددة"/>
                                <ComboBoxItem Content="تسديد جزئي"/>
                                <ComboBoxItem Content="مسددة"/>
                            </ComboBox>

                            <TextBlock Grid.Column="2" Text="الفترة:" VerticalAlignment="Center" Margin="16,0,8,0"/>
                            <ComboBox Grid.Column="3" x:Name="RecentPeriodFilterComboBox" Width="120"
                                    SelectionChanged="RecentPeriodFilter_Changed">
                                <ComboBoxItem Content="آخر أسبوع" IsSelected="True"/>
                                <ComboBoxItem Content="آخر شهر"/>
                                <ComboBoxItem Content="آخر 3 أشهر"/>
                            </ComboBox>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="RecentInvoicesItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                              CornerRadius="8" Margin="0,0,0,12" Padding="16" Cursor="Hand"
                                              MouseLeftButtonUp="RecentInvoiceItem_Click">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#20000000" BlurRadius="8" ShadowDepth="2" Direction="270"/>
                                            </Border.Effect>
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                                        </Trigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument" Width="24" Height="24"
                                                                       Foreground="{StaticResource PrimaryGradientBrush}"
                                                                       VerticalAlignment="Top" Margin="0,0,12,0"/>

                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium" FontSize="14"/>
                                                    <TextBlock Text="{Binding Supplier.Name}" FontSize="12" Opacity="0.7" Margin="0,2,0,0"/>
                                                    <TextBlock Text="{Binding InvoiceDate, StringFormat='{}{0:yyyy/MM/dd}'}"
                                                             FontSize="11" Opacity="0.6" Margin="0,2,0,0"/>
                                                </StackPanel>

                                                <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                                    <TextBlock Text="{Binding Amount, StringFormat='{}{0:N0} د.ع'}"
                                                             FontWeight="Medium" HorizontalAlignment="Right" FontSize="14"/>
                                                    <Border Background="{Binding StatusColor}" CornerRadius="12" Padding="8,4" Margin="0,4,0,0">
                                                        <TextBlock Text="{Binding StatusText}" Foreground="White"
                                                                 FontSize="10" HorizontalAlignment="Center" FontWeight="Medium"/>
                                                    </Border>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>

                <!-- Enhanced Alerts and Notifications -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCardStyle}">
                    <Grid Margin="24">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0" Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="التنبيهات والإشعارات"
                                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                         Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                <TextBlock Text="تنبيهات مهمة تتطلب انتباهك"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.7" Margin="0,2,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="AlertSettingsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="إعدادات التنبيهات" Click="AlertSettingsButton_Click">
                                    <materialDesign:PackIcon Kind="BellSettings" Width="18" Height="18"/>
                                </Button>
                                <Button x:Name="ClearAlertsButton" Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="مسح جميع التنبيهات" Click="ClearAlertsButton_Click" Margin="8,0,0,0">
                                    <materialDesign:PackIcon Kind="BellOff" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- Alert Summary -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock x:Name="CriticalAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#F44336" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="حرجة" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock x:Name="WarningAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#FF9800" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="تحذيرية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock x:Name="InfoAlertsCount" Text="0"
                                         Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                         Foreground="#2196F3" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="معلوماتية" Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center" Opacity="0.7"/>
                            </StackPanel>
                        </Grid>

                        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                            <StackPanel x:Name="AlertsStackPanel">
                                <!-- Alerts will be populated dynamically -->
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Enhanced Quick Actions -->
            <materialDesign:Card Grid.Row="4" Style="{StaticResource ModernCardStyle}">
                <Grid Margin="32">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Margin="0,0,0,16">
                        <TextBlock Text="الإجراءات السريعة"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                 Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                        <TextBlock Text="الوصول السريع للمهام الأكثر استخداماً"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 Opacity="0.7" Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Action Categories -->
                    <Grid Grid.Row="1" Margin="0,0,0,24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="InvoiceActionsTab" Content="الفواتير"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="invoices" IsDefault="True"/>
                        <Button Grid.Column="1" x:Name="PaymentActionsTab" Content="المدفوعات"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="payments"/>
                        <Button Grid.Column="2" x:Name="SystemActionsTab" Content="النظام"
                              Style="{StaticResource MaterialDesignFlatButton}"
                              Click="ActionTab_Click" Tag="system"/>
                    </Grid>

                    <!-- Action Buttons Container -->
                    <Grid Grid.Row="2" x:Name="ActionsContainer">
                        <!-- Invoice Actions -->
                        <UniformGrid x:Name="InvoiceActionsPanel" Columns="3" Rows="2" Visibility="Visible">
                            <!-- Add Invoice -->
                            <Button x:Name="AddInvoiceButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddInvoiceButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Plus" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إضافة فاتورة" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إنشاء فاتورة جديدة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- View Invoices -->
                            <Button x:Name="ViewInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ViewInvoicesButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="FileDocument" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="عرض الفواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعراض جميع الفواتير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Search Invoices -->
                            <Button x:Name="SearchInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SearchButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Magnify" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="البحث المتقدم" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="البحث في الفواتير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Manage Suppliers -->
                            <Button x:Name="SuppliersButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SuppliersButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إدارة الموردين" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إضافة وتعديل الموردين" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Invoice Reports -->
                            <Button x:Name="InvoiceReportsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ReportsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تقارير الفواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إحصائيات وتقارير" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Import Invoices -->
                            <Button x:Name="ImportInvoicesButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ImportInvoicesButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#795548" Offset="0"/>
                                        <GradientStop Color="#8D6E63" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Import" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="استيراد فواتير" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استيراد من ملف Excel" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>

                        <!-- Payment Actions -->
                        <UniformGrid x:Name="PaymentActionsPanel" Columns="3" Rows="2" Visibility="Collapsed">
                            <!-- Add Payment -->
                            <Button x:Name="AddPaymentButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddPaymentButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CreditCard" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تسجيل دفعة" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إضافة دفعة جديدة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Add Multi Payment -->
                            <Button x:Name="AddMultiPaymentButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AddMultiPaymentButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF6B73" Offset="0"/>
                                        <GradientStop Color="#009FFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CreditCardMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="وصل متعدد" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="دفع عدة فواتير معاً" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- View Payments -->
                            <Button x:Name="ViewPaymentsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ViewPaymentsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="عرض المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعراض جميع المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Payment Reports -->
                            <Button x:Name="PaymentReportsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="PaymentReportsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ChartBar" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تقارير المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إحصائيات المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Reconciliation -->
                            <Button x:Name="ReconciliationButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ReconciliationButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="ScaleBalance" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تسوية الحسابات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="مطابقة المدفوعات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Payment Reminders -->
                            <Button x:Name="PaymentRemindersButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="PaymentRemindersButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="BellAlert" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تذكيرات الدفع" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إدارة التذكيرات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Export Payments -->
                            <Button x:Name="ExportPaymentsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="ExportPaymentsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#795548" Offset="0"/>
                                        <GradientStop Color="#8D6E63" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Export" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="تصدير المدفوعات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="تصدير إلى Excel" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>

                        <!-- System Actions -->
                        <UniformGrid x:Name="SystemActionsPanel" Columns="3" Rows="2" Visibility="Collapsed">
                            <!-- Settings -->
                            <Button x:Name="SettingsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SettingsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#607D8B" Offset="0"/>
                                        <GradientStop Color="#90A4AE" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Settings" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="الإعدادات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="إعدادات النظام" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Backup -->
                            <Button x:Name="BackupButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="BackupButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#4CAF50" Offset="0"/>
                                        <GradientStop Color="#8BC34A" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="DatabaseExport" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="نسخ احتياطي" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="حفظ البيانات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- Restore -->
                            <Button x:Name="RestoreButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="RestoreButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#FF9800" Offset="0"/>
                                        <GradientStop Color="#FFC107" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="DatabaseImport" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="استعادة البيانات" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="استعادة من نسخة احتياطية" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- User Management -->
                            <Button x:Name="UserManagementButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="UserManagementButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#673AB7" Offset="0"/>
                                        <GradientStop Color="#9C27B0" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="AccountMultiple" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="إدارة المستخدمين" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="المستخدمين والصلاحيات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- System Logs -->
                            <Button x:Name="SystemLogsButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="SystemLogsButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#2196F3" Offset="0"/>
                                        <GradientStop Color="#03DAC6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="FileDocumentOutline" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="سجلات النظام" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="عرض سجلات العمليات" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>

                            <!-- About -->
                            <Button x:Name="AboutButton" Style="{StaticResource ModernActionButtonStyle}"
                                   Click="AboutButton_Click">
                                <Button.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#E91E63" Offset="0"/>
                                        <GradientStop Color="#F44336" Offset="1"/>
                                    </LinearGradientBrush>
                                </Button.Background>
                                <StackPanel>
                                    <materialDesign:PackIcon Kind="Information" Width="28" Height="28" Margin="0,0,0,8" Foreground="White"/>
                                    <TextBlock Text="حول البرنامج" FontWeight="Medium" Foreground="White"/>
                                    <TextBlock Text="معلومات النسخة" FontSize="11" Opacity="0.8" Foreground="White"/>
                                </StackPanel>
                            </Button>
                        </UniformGrid>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
