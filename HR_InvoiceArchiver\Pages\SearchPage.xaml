<UserControl x:Class="HR_InvoiceArchiver.Pages.SearchPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F5F6FA">

    <UserControl.Resources>
        <!-- Modern Button Styles -->
        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا -->
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Modern Header -->
        <materialDesign:Card Grid.Row="0" Padding="32" Margin="0,0,0,24" materialDesign:ElevationAssist.Elevation="Dp8">
            <materialDesign:Card.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </materialDesign:Card.Background>
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="#667eea" Opacity="0.4" BlurRadius="25" ShadowDepth="10"/>
            </materialDesign:Card.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                        <materialDesign:PackIcon Kind="DatabaseSearch" Width="36" Height="36" Foreground="White" VerticalAlignment="Center" Margin="0,0,16,0"/>
                        <StackPanel>
                            <TextBlock Text="البحث الذكي المتقدم"
                                     FontSize="32" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="اكتشف البيانات بسرعة وذكاء"
                                     FontSize="16" Foreground="#E8EAF6" Opacity="0.9" Margin="0,4,0,0"/>
                        </StackPanel>
                    </StackPanel>
                    <TextBlock Text="ابحث في الفواتير والموردين والمدفوعات باستخدام معايير متعددة ومتقدمة مع اقتراحات ذكية ونتائج فورية"
                             FontSize="16" Foreground="#E8EAF6" Opacity="0.85" TextWrapping="Wrap"/>
                </StackPanel>

                <!-- Quick Stats -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="#FFFFFF20" CornerRadius="12" Padding="16,8" Margin="0,0,12,0">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickStatsInvoices" Text="0" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="فاتورة" FontSize="12" Foreground="#E8EAF6" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    <Border Background="#FFFFFF20" CornerRadius="12" Padding="16,8">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock x:Name="QuickStatsSuppliers" Text="0" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                            <TextBlock Text="مورد" FontSize="12" Foreground="#E8EAF6" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Simple Search Section -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Main Search Bar -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="QuickSearchTextBox" Grid.Column="0"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           materialDesign:HintAssist.Hint="ابحث في رقم الفاتورة، اسم المورد، أو الوصف..."
                           materialDesign:TextFieldAssist.HasLeadingIcon="True"
                           materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                           FontSize="16" Height="56"
                           TextChanged="QuickSearchTextBox_TextChanged"/>

                    <Button x:Name="SearchButton" Grid.Column="1"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Background="#667eea" BorderBrush="#667eea"
                          Height="56" Padding="20,0" Margin="12,0,0,0"
                          Click="SearchButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20" Margin="0,0,8,0"/>
                            <TextBlock Text="بحث" FontSize="14"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="ClearButton" Grid.Column="2"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Height="56" Padding="16,0" Margin="8,0,0,0"
                          Click="ClearButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,6,0"/>
                            <TextBlock Text="مسح" FontSize="14"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- Simple Filters -->
                <Expander x:Name="FiltersExpander" Grid.Row="1" Header="فلاتر البحث" IsExpanded="False">
                    <Grid Margin="0,16,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- First Row Filters -->
                        <TextBox x:Name="InvoiceNumberTextBox" Grid.Row="0" Grid.Column="0"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="رقم الفاتورة"
                               materialDesign:TextFieldAssist.HasLeadingIcon="True"
                               materialDesign:TextFieldAssist.LeadingIcon="FileDocument"
                               Margin="0,0,8,0" Height="48"
                               TextChanged="SearchTextBox_TextChanged"/>

                        <ComboBox x:Name="SupplierComboBox" Grid.Row="0" Grid.Column="1"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:HintAssist.Hint="اختر المورد"
                                DisplayMemberPath="Name"
                                SelectedValuePath="Id"
                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                materialDesign:TextFieldAssist.LeadingIcon="Account"
                                Margin="4,0,4,0" Height="48"
                                SelectionChanged="SupplierComboBox_SelectionChanged"/>

                        <ComboBox x:Name="StatusComboBox" Grid.Row="0" Grid.Column="2"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                materialDesign:HintAssist.Hint="حالة الدفع"
                                materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                materialDesign:TextFieldAssist.LeadingIcon="CheckCircle"
                                Margin="8,0,0,0" Height="48"
                                SelectionChanged="StatusComboBox_SelectionChanged">
                            <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                            <ComboBoxItem Content="مدفوعة"/>
                            <ComboBoxItem Content="غير مدفوعة"/>
                            <ComboBoxItem Content="مدفوعة جزئياً"/>
                        </ComboBox>

                        <!-- Second Row Filters -->
                        <DatePicker x:Name="FromDatePicker" Grid.Row="1" Grid.Column="0"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                  materialDesign:HintAssist.Hint="من تاريخ"
                                  materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                  materialDesign:TextFieldAssist.LeadingIcon="CalendarStart"
                                  Margin="0,8,8,0" Height="48"
                                  SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                        <DatePicker x:Name="ToDatePicker" Grid.Row="1" Grid.Column="1"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                  materialDesign:HintAssist.Hint="إلى تاريخ"
                                  materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                  materialDesign:TextFieldAssist.LeadingIcon="CalendarEnd"
                                  Margin="4,8,4,0" Height="48"
                                  SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                        <TextBox x:Name="AmountTextBox" Grid.Row="1" Grid.Column="2"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               materialDesign:HintAssist.Hint="المبلغ (IQD)"
                               materialDesign:TextFieldAssist.HasLeadingIcon="True"
                               materialDesign:TextFieldAssist.LeadingIcon="CurrencyUsd"
                               Margin="8,8,0,0" Height="48"
                               TextChanged="SearchTextBox_TextChanged"/>
                    </Grid>
                </Expander>

            </Grid>
        </materialDesign:Card>



        <!-- Enhanced Statistics Cards Section -->
        <materialDesign:Card Grid.Row="2" Padding="0" Margin="0,0,0,24" materialDesign:ElevationAssist.Elevation="Dp6">
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="20" ShadowDepth="8"/>
            </materialDesign:Card.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Stats Header -->
                <Border Grid.Row="0" Background="#F8F9FF" CornerRadius="8,8,0,0" Padding="24,16">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,12,0"/>
                        <StackPanel>
                            <TextBlock Text="إحصائيات البحث المباشرة" FontSize="18" FontWeight="SemiBold" Foreground="#2D3748"/>
                            <TextBlock Text="تحديث فوري مع كل تغيير في معايير البحث" FontSize="13" Foreground="#718096" Margin="0,2,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Stats Content -->
                <Grid Grid.Row="1" Margin="24">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Results Card -->
                <materialDesign:Card Grid.Column="0" Padding="20" Margin="0,0,12,0" materialDesign:ElevationAssist.Elevation="Dp3"
                                   Background="White" Cursor="Hand" MouseEnter="StatsCard_MouseEnter" MouseLeave="StatsCard_MouseLeave">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#667eea" Opacity="0.2" BlurRadius="12" ShadowDepth="4"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#667eea" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Left" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1">
                            <TextBlock x:Name="TotalResultsText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Left"/>
                            <TextBlock Text="إجمالي النتائج" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#F7FAFC" CornerRadius="6" Padding="8,4" Margin="0,8,0,0">
                            <TextBlock x:Name="ResultsPercentageText" Text="100%" FontSize="12"
                                     Foreground="#667eea" FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Total Amount Card -->
                <materialDesign:Card Grid.Column="1" Padding="20" Margin="6,0,6,0" materialDesign:ElevationAssist.Elevation="Dp3"
                                   Background="White" Cursor="Hand" MouseEnter="StatsCard_MouseEnter" MouseLeave="StatsCard_MouseLeave">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#10B981" Opacity="0.2" BlurRadius="12" ShadowDepth="4"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#10B981" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Left" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1">
                            <TextBlock x:Name="TotalAmountText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Left"/>
                            <TextBlock Text="إجمالي المبلغ" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#F0FDF4" CornerRadius="6" Padding="8,4" Margin="0,8,0,0">
                            <TextBlock Text="دينار عراقي" FontSize="12"
                                     Foreground="#10B981" FontWeight="Medium" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Paid Amount Card -->
                <materialDesign:Card Grid.Column="2" Padding="20" Margin="6,0,6,0" materialDesign:ElevationAssist.Elevation="Dp3"
                                   Background="White" Cursor="Hand" MouseEnter="StatsCard_MouseEnter" MouseLeave="StatsCard_MouseLeave">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#F59E0B" Opacity="0.2" BlurRadius="12" ShadowDepth="4"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#F59E0B" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Left" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CheckCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1">
                            <TextBlock x:Name="PaidAmountText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Left"/>
                            <TextBlock Text="المبلغ المدفوع" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#FFFBEB" CornerRadius="6" Padding="8,4" Margin="0,8,0,0">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock x:Name="PaidPercentageText" Text="0%" FontSize="12"
                                         Foreground="#F59E0B" FontWeight="Medium" Margin="0,0,4,0"/>
                                <TextBlock Text="مدفوع" FontSize="12" Foreground="#F59E0B" FontWeight="Medium"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </materialDesign:Card>

                <!-- Outstanding Amount Card -->
                <materialDesign:Card Grid.Column="3" Padding="20" Margin="12,0,0,0" materialDesign:ElevationAssist.Elevation="Dp3"
                                   Background="White" Cursor="Hand" MouseEnter="StatsCard_MouseEnter" MouseLeave="StatsCard_MouseLeave">
                    <materialDesign:Card.Effect>
                        <DropShadowEffect Color="#EF4444" Opacity="0.2" BlurRadius="12" ShadowDepth="4"/>
                    </materialDesign:Card.Effect>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Border Grid.Row="0" Background="#EF4444" CornerRadius="20" Width="40" Height="40"
                                HorizontalAlignment="Left" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="AlertCircle" Width="20" Height="20" Foreground="White"
                                                   HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Row="1">
                            <TextBlock x:Name="OutstandingAmountText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#2D3748" HorizontalAlignment="Left"/>
                            <TextBlock Text="المبلغ المتبقي" FontSize="14" FontWeight="Medium"
                                     Foreground="#718096" Margin="0,4,0,0"/>
                        </StackPanel>

                        <Border Grid.Row="2" Background="#FEF2F2" CornerRadius="6" Padding="8,4" Margin="0,8,0,0">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock x:Name="OutstandingPercentageText" Text="0%" FontSize="12"
                                         Foreground="#EF4444" FontWeight="Medium" Margin="0,0,4,0"/>
                                <TextBlock Text="متبقي" FontSize="12" Foreground="#EF4444" FontWeight="Medium"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </Grid>
        </materialDesign:Card>

        <!-- Smart Results Section -->
        <materialDesign:Card Grid.Row="3" materialDesign:ElevationAssist.Elevation="Dp6" Background="White">
            <materialDesign:Card.Effect>
                <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="20" ShadowDepth="8"/>
            </materialDesign:Card.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Results Header -->
                <Border Grid.Row="0" Background="#F8F9FF" CornerRadius="8,8,0,0" Padding="24,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="TableSearch" Width="28" Height="28" Foreground="#667eea" VerticalAlignment="Center"/>
                            <StackPanel Margin="16,0,0,0">
                                <TextBlock Text="نتائج البحث الذكي" FontSize="22" FontWeight="Bold" Foreground="#2D3748"/>
                                <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                    <Border Background="#667eea" CornerRadius="12" Padding="12,4" Margin="0,0,12,0">
                                        <TextBlock x:Name="ResultsCountText" Text="0 نتيجة" FontSize="13" FontWeight="SemiBold" Foreground="White"/>
                                    </Border>
                                    <Border Background="#10B981" CornerRadius="12" Padding="12,4" Margin="0,0,12,0">
                                        <TextBlock x:Name="SearchTimeText" Text="0ms" FontSize="13" FontWeight="SemiBold" Foreground="White"/>
                                    </Border>
                                    <Border Background="#F59E0B" CornerRadius="12" Padding="12,4">
                                        <TextBlock x:Name="FilterStatusText" Text="بدون فلاتر" FontSize="13" FontWeight="SemiBold" Foreground="White"/>
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button x:Name="ViewModeButton" Style="{StaticResource MaterialDesignIconButton}"
                                    Width="44" Height="44" Margin="0,0,8,0"
                                    ToolTip="تغيير طريقة العرض" Click="ViewModeButton_Click">
                                <materialDesign:PackIcon x:Name="ViewModeIcon" Kind="ViewList" Width="20" Height="20"/>
                            </Button>
                            <Button x:Name="RefreshResultsButton" Style="{StaticResource MaterialDesignIconButton}"
                                    Width="44" Height="44" Margin="0,0,8,0"
                                    ToolTip="تحديث النتائج" Click="RefreshResultsButton_Click">
                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                            </Button>
                            <Button x:Name="FullScreenButton" Style="{StaticResource MaterialDesignIconButton}"
                                    Width="44" Height="44"
                                    ToolTip="عرض بملء الشاشة" Click="FullScreenButton_Click">
                                <materialDesign:PackIcon Kind="Fullscreen" Width="20" Height="20"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Results Content -->
                <Grid Grid.Row="1" Margin="0">


                <!-- Smart Results Grid -->
                <Grid Grid.Row="0" Margin="24">
                    <!-- Loading Panel -->
                    <materialDesign:Card x:Name="LoadingPanel" Visibility="Collapsed"
                                       Padding="60" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       materialDesign:ElevationAssist.Elevation="Dp6" Background="White">
                        <materialDesign:Card.Effect>
                            <DropShadowEffect Color="#667eea" Opacity="0.2" BlurRadius="15" ShadowDepth="6"/>
                        </materialDesign:Card.Effect>
                        <StackPanel HorizontalAlignment="Center">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Value="0" IsIndeterminate="True" Width="60" Height="60" Margin="0,0,0,20"
                                       Foreground="#667eea"/>
                            <TextBlock Text="جاري البحث الذكي..." FontSize="18" FontWeight="Medium"
                                     Foreground="#2D3748" HorizontalAlignment="Center"/>
                            <TextBlock Text="يتم تحليل البيانات وتطبيق الفلاتر..." FontSize="14"
                                     Foreground="#718096" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Empty State Panel -->
                    <materialDesign:Card x:Name="EmptyStatePanel" Visibility="Collapsed"
                                       Padding="60" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       materialDesign:ElevationAssist.Elevation="Dp3" Background="#F8F9FF">
                        <StackPanel HorizontalAlignment="Center" MaxWidth="400">
                            <materialDesign:PackIcon Kind="DatabaseSearchOutline" Width="80" Height="80"
                                                   Foreground="#CBD5E0" Margin="0,0,0,24"/>
                            <TextBlock Text="لا توجد نتائج مطابقة" FontSize="20" FontWeight="SemiBold"
                                     Foreground="#2D3748" HorizontalAlignment="Center" Margin="0,0,0,12"/>
                            <TextBlock Text="جرب تعديل معايير البحث أو استخدم البحث السريع" FontSize="15"
                                     Foreground="#718096" HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,20"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="مسح الفلاتر" Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Click="ClearButton_Click" Margin="0,0,12,0"/>
                                <Button Content="بحث جديد" Style="{StaticResource MaterialDesignRaisedButton}"
                                        Background="#667eea" BorderBrush="#667eea" Click="NewSearchButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Enhanced Smart Data Grid -->
                    <DataGrid x:Name="ResultsDataGrid"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            IsReadOnly="True"
                            SelectionMode="Single"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column"
                            materialDesign:DataGridAssist.CellPadding="16"
                            materialDesign:DataGridAssist.ColumnHeaderPadding="16"
                            Background="White"
                            RowBackground="White"
                            AlternatingRowBackground="#FAFBFC"
                            FontSize="14"
                            MouseDoubleClick="ResultsDataGrid_MouseDoubleClick"
                            SelectionChanged="ResultsDataGrid_SelectionChanged">

                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                <Setter Property="Background" Value="#667eea"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="52"/>
                                <Setter Property="BorderBrush" Value="#5a67d8"/>
                                <Setter Property="BorderThickness" Value="0,0,1,0"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>

                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                <Setter Property="Height" Value="56"/>
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F0F4FF"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#E0E7FF"/>
                                        <Setter Property="BorderBrush" Value="#667eea"/>
                                        <Setter Property="BorderThickness" Value="2,0,0,0"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>

                        <DataGrid.Columns>
                            <!-- Invoice Number Column with Icon -->
                            <DataGridTemplateColumn Header="رقم الفاتورة" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"
                                                                   Foreground="#667eea" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding InvoiceNumber}" FontWeight="SemiBold"
                                                     Foreground="#667eea" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Supplier Column with Icon -->
                            <DataGridTemplateColumn Header="المورد" Width="180">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Account" Width="16" Height="16"
                                                                   Foreground="#718096" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding SupplierName}" FontWeight="Medium"
                                                     Foreground="#2D3748" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Date Column with Icon -->
                            <DataGridTemplateColumn Header="التاريخ" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="Calendar" Width="16" Height="16"
                                                                   Foreground="#718096" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding Date, StringFormat=dd/MM/yyyy}"
                                                     Foreground="#4A5568" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Amount Column with Currency -->
                            <DataGridTemplateColumn Header="المبلغ الإجمالي" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#F0FDF4" CornerRadius="6" Padding="8,4">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding Amount, StringFormat=N0}" FontWeight="SemiBold"
                                                         Foreground="#10B981" VerticalAlignment="Center"/>
                                                <TextBlock Text=" د.ع" FontSize="12" Foreground="#10B981"
                                                         VerticalAlignment="Center" Margin="4,0,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Paid Amount Column -->
                            <DataGridTemplateColumn Header="المبلغ المدفوع" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border Background="#FFFBEB" CornerRadius="6" Padding="8,4">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding PaidAmount, StringFormat=N0}" FontWeight="SemiBold"
                                                         Foreground="#F59E0B" VerticalAlignment="Center"/>
                                                <TextBlock Text=" د.ع" FontSize="12" Foreground="#F59E0B"
                                                         VerticalAlignment="Center" Margin="4,0,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Status Column with Enhanced Design -->
                            <DataGridTemplateColumn Header="الحالة" Width="130">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="16" Padding="12,6" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="مدفوع">
                                                            <Setter Property="Background" Value="#DCFCE7"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="غير مدفوع">
                                                            <Setter Property="Background" Value="#FEE2E2"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="مدفوع جزئياً">
                                                            <Setter Property="Background" Value="#FEF3C7"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Width="14" Height="14" VerticalAlignment="Center" Margin="0,0,6,0">
                                                    <materialDesign:PackIcon.Style>
                                                        <Style TargetType="materialDesign:PackIcon">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Status}" Value="مدفوع">
                                                                    <Setter Property="Kind" Value="CheckCircle"/>
                                                                    <Setter Property="Foreground" Value="#16A34A"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="غير مدفوع">
                                                                    <Setter Property="Kind" Value="CloseCircle"/>
                                                                    <Setter Property="Foreground" Value="#DC2626"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="مدفوع جزئياً">
                                                                    <Setter Property="Kind" Value="ClockOutline"/>
                                                                    <Setter Property="Foreground" Value="#D97706"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </materialDesign:PackIcon.Style>
                                                </materialDesign:PackIcon>
                                                <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="SemiBold" VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding Status}" Value="مدفوع">
                                                                    <Setter Property="Foreground" Value="#16A34A"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="غير مدفوع">
                                                                    <Setter Property="Foreground" Value="#DC2626"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding Status}" Value="مدفوع جزئياً">
                                                                    <Setter Property="Foreground" Value="#D97706"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Description Column -->
                            <DataGridTemplateColumn Header="الوصف" Width="200">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Description}" Foreground="#4A5568"
                                                 TextWrapping="Wrap" MaxWidth="180" VerticalAlignment="Center"
                                                 ToolTip="{Binding Description}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- Actions Column -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="160">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Width="32" Height="32" Margin="2"
                                                    ToolTip="عرض التفاصيل"
                                                    Click="ViewDetailsButton_Click"
                                                    Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16" Foreground="#667eea"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Width="32" Height="32" Margin="2"
                                                    ToolTip="تعديل الفاتورة"
                                                    Click="EditInvoiceButton_Click"
                                                    Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16" Foreground="#10B981"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Width="32" Height="32" Margin="2"
                                                    ToolTip="إضافة دفعة"
                                                    Click="AddPaymentButton_Click"
                                                    Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="16" Height="16" Foreground="#F59E0B"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                    Width="32" Height="32" Margin="2"
                                                    ToolTip="طباعة الفاتورة"
                                                    Click="PrintInvoiceButton_Click"
                                                    Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" Foreground="#718096"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>

                <!-- Smart Footer Panel -->
                <Border Grid.Row="1" Background="#F8F9FF" CornerRadius="0,0,8,8" Padding="24,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Results Info -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="InformationOutline" Width="20" Height="20"
                                                   Foreground="#667eea" VerticalAlignment="Center"/>
                            <TextBlock x:Name="ResultsInfoText" Text="استخدم البحث السريع أو الفلاتر المتقدمة للحصول على نتائج دقيقة"
                                     FontSize="14" Foreground="#4A5568" Margin="12,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- Pagination -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,20,0">
                            <Button x:Name="PrevPageButton" Style="{StaticResource MaterialDesignIconButton}"
                                    Width="36" Height="36" Margin="0,0,8,0"
                                    ToolTip="الصفحة السابقة" Click="PrevPageButton_Click">
                                <materialDesign:PackIcon Kind="ChevronLeft" Width="20" Height="20"/>
                            </Button>
                            <Border Background="White" CornerRadius="8" Padding="12,6" Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="صفحة " FontSize="13" Foreground="#4A5568" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="CurrentPageText" Text="1" FontSize="13" FontWeight="SemiBold"
                                             Foreground="#667eea" VerticalAlignment="Center"/>
                                    <TextBlock Text=" من " FontSize="13" Foreground="#4A5568" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="TotalPagesText" Text="1" FontSize="13" FontWeight="SemiBold"
                                             Foreground="#667eea" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            <Button x:Name="NextPageButton" Style="{StaticResource MaterialDesignIconButton}"
                                    Width="36" Height="36"
                                    ToolTip="الصفحة التالية" Click="NextPageButton_Click">
                                <materialDesign:PackIcon Kind="ChevronRight" Width="20" Height="20"/>
                            </Button>
                        </StackPanel>

                        <!-- Export Actions -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                            <Button x:Name="ExportExcelButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Height="36" Padding="16,0"
                                  Click="ExportExcelButton_Click"
                                  Margin="0,0,8,0"
                                  ToolTip="تصدير النتائج إلى Excel">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="Excel" FontSize="13"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ExportPdfButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Height="36" Padding="16,0"
                                  Click="ExportPdfButton_Click"
                                  Margin="0,0,8,0"
                                  ToolTip="تصدير النتائج إلى PDF">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilePdfBox" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="PDF" FontSize="13"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ShareResultsButton"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Background="#667eea" BorderBrush="#667eea"
                                  Height="36" Padding="16,0"
                                  Click="ShareResultsButton_Click"
                                  ToolTip="مشاركة النتائج">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Share" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="مشاركة" FontSize="13"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>
                </Grid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
