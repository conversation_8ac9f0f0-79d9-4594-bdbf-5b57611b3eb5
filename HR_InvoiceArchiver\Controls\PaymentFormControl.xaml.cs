using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;

namespace HR_InvoiceArchiver.Controls
{
    public partial class PaymentFormControl : UserControl, INotifyPropertyChanged
    {
        private readonly IPaymentService _paymentService;
        private readonly IInvoiceService _invoiceService;
        private readonly IToastService _toastService;
        
        public event PropertyChangedEventHandler? PropertyChanged;
        public event EventHandler? PaymentSaved;
        public event EventHandler? FormClosed;

        
        private Payment? _currentPayment;
        private bool _isEditMode;
        private Invoice? _selectedInvoice;
        private bool _isUpdatingAutomatically = false;
        
        public ObservableCollection<Invoice> AvailableInvoices { get; set; } = new();
        
        // Properties for data binding
        public string ReceiptNumber { get; set; } = string.Empty;
        public decimal PaymentAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        public PaymentMethod SelectedPaymentMethod { get; set; } = PaymentMethod.Cash;
        public PaymentStatus SelectedPaymentStatus { get; set; } = PaymentStatus.FullPayment;

        public string Notes { get; set; } = string.Empty;
        public string AttachmentPath { get; set; } = string.Empty;
        
        public PaymentFormControl(Payment? payment = null)
        {
            // Initialize services from DI container
            _paymentService = App.ServiceProvider.GetRequiredService<IPaymentService>();
            _invoiceService = App.ServiceProvider.GetRequiredService<IInvoiceService>();
            _toastService = App.ServiceProvider.GetRequiredService<IToastService>();
            
            _currentPayment = payment;
            _isEditMode = payment != null;
            
            InitializeComponent();
            DataContext = this;
            
            Loaded += PaymentFormControl_Loaded;
        }
        
        private async void PaymentFormControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Start slide-in animation
            var slideInStoryboard = (Storyboard)Resources["SlideInAnimation"];
            slideInStoryboard.Begin();

            // Set default payment date to today if not in edit mode
            if (!_isEditMode)
            {
                PaymentDatePicker.SelectedDate = DateTime.Now;
            }

            // Load available invoices
            await LoadAvailableInvoicesAsync();

            // Load payment data if in edit mode
            if (_isEditMode && _currentPayment != null)
            {
                LoadPaymentData();
                SaveButtonText.Text = "تحديث الوصل";
            }
        }
        
        private async Task LoadAvailableInvoicesAsync()
        {
            try
            {
                var invoices = await _invoiceService.GetAllInvoicesAsync();
                
                // Filter to show only unpaid or partially paid invoices
                var availableInvoices = invoices.Where(i => 
                    i.Status == InvoiceStatus.Unpaid || 
                    i.Status == InvoiceStatus.PartiallyPaid).ToList();
                
                AvailableInvoices.Clear();
                foreach (var invoice in availableInvoices)
                {
                    AvailableInvoices.Add(invoice);
                }
                
                InvoiceComboBox.ItemsSource = AvailableInvoices;
                
                // If editing, select the current invoice
                if (_isEditMode && _currentPayment != null)
                {
                    var currentInvoice = AvailableInvoices.FirstOrDefault(i => i.Id == _currentPayment.InvoiceId);
                    if (currentInvoice != null)
                    {
                        InvoiceComboBox.SelectedItem = currentInvoice;
                    }
                }
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الفواتير", ex.Message);
            }
        }
        
        private void LoadPaymentData()
        {
            if (_currentPayment == null) return;
            
            ReceiptNumber = _currentPayment.ReceiptNumber;
            PaymentAmount = _currentPayment.Amount;
            PaymentDate = _currentPayment.PaymentDate;
            SelectedPaymentMethod = _currentPayment.Method;

            Notes = _currentPayment.Notes ?? string.Empty;
            AttachmentPath = _currentPayment.AttachmentPath ?? string.Empty;
            
            // Update UI controls
            ReceiptNumberTextBox.Text = ReceiptNumber;
            PaymentAmountTextBox.Text = PaymentAmount.ToString("F0");
            PaymentDatePicker.SelectedDate = PaymentDate;
            PaymentMethodComboBox.SelectedIndex = (int)SelectedPaymentMethod;

            NotesTextBox.Text = Notes;
            AttachmentPathTextBox.Text = AttachmentPath;
            
            OnPropertyChanged(nameof(ReceiptNumber));
            OnPropertyChanged(nameof(PaymentAmount));
            OnPropertyChanged(nameof(PaymentDate));
            OnPropertyChanged(nameof(SelectedPaymentMethod));

            OnPropertyChanged(nameof(Notes));
            OnPropertyChanged(nameof(AttachmentPath));
        }
        
        private void InvoiceComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (InvoiceComboBox.SelectedItem is Invoice selectedInvoice)
            {
                _selectedInvoice = selectedInvoice;
                ShowInvoiceInfo(selectedInvoice);
            }
            else
            {
                InvoiceInfoPanel.Visibility = Visibility.Collapsed;
            }
        }
        
        private void ShowInvoiceInfo(Invoice invoice)
        {
            var infoText = $"📋 رقم الفاتورة: {invoice.InvoiceNumber}\n" +
                          $"🏢 المورد: {invoice.Supplier?.Name ?? "غير محدد"}\n" +
                          $"💰 المبلغ الإجمالي: {invoice.Amount:N0} د.ع\n" +
                          $"💳 المبلغ المدفوع: {invoice.PaidAmount:N0} د.ع\n" +
                          $"⏳ المبلغ المتبقي: {invoice.RemainingAmount:N0} د.ع\n" +
                          $"📅 تاريخ الاستحقاق: {invoice.DueDate:yyyy/MM/dd}";

            InvoiceInfoTextBlock.Text = infoText;
            InvoiceInfoPanel.Visibility = Visibility.Visible;

            // Auto-fill payment amount with remaining amount
            if (!_isEditMode && invoice.RemainingAmount > 0)
            {
                PaymentAmountTextBox.Text = invoice.RemainingAmount.ToString("F0");
            }

            // Update payment status after setting amount
            UpdatePaymentStatusSmart();
        }
        
        private void PaymentAmountTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        private void PaymentAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // تجنب التحديث المتكرر أثناء التحديث التلقائي
            if (_isUpdatingAutomatically)
                return;

            // التحقق من صحة المبلغ مع الحالة المختارة (إذا كانت محددة)
            if (!string.IsNullOrWhiteSpace(PaymentAmountTextBox.Text) &&
                decimal.TryParse(PaymentAmountTextBox.Text, out decimal paymentAmount))
            {
                // إذا كان في وضع "تسديد وبخصم"، احسب الخصم تلقائياً
                if (PaymentStatusComboBox.SelectedItem is ComboBoxItem selectedItem &&
                    selectedItem.Content.ToString() == "تسديد وبخصم" &&
                    _selectedInvoice != null)
                {
                    decimal calculatedDiscount = _selectedInvoice.Amount - paymentAmount;
                    if (calculatedDiscount >= 0)
                    {
                        _isUpdatingAutomatically = true;
                        DiscountAmount = calculatedDiscount;
                        DiscountAmountTextBox.Text = calculatedDiscount.ToString("F0");
                        _isUpdatingAutomatically = false;
                    }
                }

                // التحقق من صحة المبلغ مع الحالة المختارة
                ValidatePaymentAmount(paymentAmount);
            }
            else
            {
                // إخفاء مؤشر الحالة إذا لم يكن هناك مبلغ
                StatusIndicatorPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdatePaymentStatusSmart()
        {
            if (_selectedInvoice == null)
            {
                PaymentStatusComboBox.IsEnabled = false;
                StatusIndicatorPanel.Visibility = Visibility.Collapsed;
                return;
            }

            // Enable payment status field when invoice is selected
            PaymentStatusComboBox.IsEnabled = true;

            // If payment amount is entered, validate it against the selected status
            if (!string.IsNullOrWhiteSpace(PaymentAmountTextBox.Text) &&
                decimal.TryParse(PaymentAmountTextBox.Text, out decimal paymentAmount))
            {
                PaymentAmount = paymentAmount;
                ValidatePaymentAmount(paymentAmount);
            }
            else
            {
                StatusIndicatorPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void ValidatePaymentAmount(decimal paymentAmount)
        {
            if (_selectedInvoice == null) return;

            decimal invoiceAmount = _selectedInvoice.Amount;
            decimal remainingAmount = _selectedInvoice.RemainingAmount;

            var currentSelection = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
            string currentStatus = currentSelection?.Content?.ToString() ?? "";

            switch (currentStatus)
            {
                case "تسديد جزئي":
                    if (paymentAmount >= remainingAmount)
                    {
                        ShowStatusIndicator("⚠️ المبلغ كبير للتسديد الجزئي - اختر 'تسديد كامل'", "#FF9800", "AlertCircle");
                    }
                    else if (paymentAmount > 0)
                    {
                        decimal remaining = remainingAmount - paymentAmount;
                        ShowStatusIndicator($"✅ تسديد جزئي - المتبقي: {remaining:F0} د.ع", "#FF9800", "CheckCircle");
                    }
                    break;

                case "تسديد كامل":
                    if (paymentAmount < remainingAmount)
                    {
                        ShowStatusIndicator("⚠️ المبلغ أقل من المتبقي - اختر 'تسديد جزئي'", "#FF9800", "AlertCircle");
                    }
                    else if (paymentAmount >= remainingAmount)
                    {
                        ShowStatusIndicator("✅ تم تسديد الفاتورة بالكامل", "#4CAF50", "CheckCircle");
                    }
                    break;

                case "تسديد وبخصم":
                    decimal potentialDiscount = invoiceAmount - paymentAmount;
                    if (potentialDiscount > 0)
                    {
                        decimal discountPercentage = (potentialDiscount / invoiceAmount) * 100;
                        ShowStatusIndicator($"✅ تسديد وبخصم {discountPercentage:F1}% - خصم: {potentialDiscount:F0} د.ع", "#9C27B0", "CheckCircle");

                        // Update discount field automatically
                        _isUpdatingAutomatically = true;
                        DiscountAmount = potentialDiscount;
                        DiscountAmountTextBox.Text = potentialDiscount.ToString("F0");
                        _isUpdatingAutomatically = false;
                    }
                    else
                    {
                        ShowStatusIndicator("⚠️ لا يمكن أن يكون المبلغ أكبر من قيمة الفاتورة", "#FF5722", "AlertCircle");
                    }
                    break;

                default:
                    // No status selected - suggest appropriate status
                    if (paymentAmount >= remainingAmount)
                    {
                        ShowStatusIndicator("💡 اختر 'تسديد كامل' لهذا المبلغ", "#2196F3", "Lightbulb");
                    }
                    else if (paymentAmount > 0)
                    {
                        ShowStatusIndicator("💡 اختر 'تسديد جزئي' أو 'تسديد وبخصم'", "#2196F3", "Lightbulb");
                    }
                    break;
            }
        }

        private void ShowStatusIndicator(string message, string color, string iconKind)
        {
            StatusMessageText.Text = message;
            StatusMessageText.Foreground = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));

            // Update icon based on status
            switch (iconKind)
            {
                case "CheckCircle":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.CheckCircle;
                    break;
                case "AlertCircle":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.AlertCircle;
                    break;
                case "Lightbulb":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Lightbulb;
                    break;
                case "Percent":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Percent;
                    break;
                case "Info":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.InformationOutline;
                    break;
                case "Clock":
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.ClockOutline;
                    break;
                default:
                    StatusIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.Information;
                    break;
            }

            StatusIcon.Foreground = StatusMessageText.Foreground;
            StatusIndicatorPanel.Visibility = Visibility.Visible;
        }

        // Search functionality for ComboBox
        private void InvoiceComboBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox == null) return;

            // Handle Enter key to select first match
            if (e.Key == Key.Enter && comboBox.IsDropDownOpen)
            {
                if (comboBox.Items.Count > 0)
                {
                    comboBox.SelectedItem = comboBox.Items[0];
                    comboBox.IsDropDownOpen = false;
                }
                e.Handled = true;
            }
        }

        private void InvoiceComboBox_DropDownOpened(object sender, EventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox == null) return;

            // If there's text in the ComboBox, filter the items
            string searchText = comboBox.Text?.Trim() ?? "";
            if (!string.IsNullOrEmpty(searchText))
            {
                FilterInvoices(searchText);
            }
        }

        private void InvoiceComboBox_KeyUp(object sender, KeyEventArgs e)
        {
            var comboBox = sender as ComboBox;
            if (comboBox == null) return;

            // Skip navigation keys
            if (e.Key == Key.Up || e.Key == Key.Down || e.Key == Key.Enter || e.Key == Key.Tab)
                return;

            string searchText = comboBox.Text?.Trim() ?? "";

            // Open dropdown if not already open and there's text
            if (!string.IsNullOrEmpty(searchText) && !comboBox.IsDropDownOpen)
            {
                comboBox.IsDropDownOpen = true;
            }

            // Filter invoices based on search text
            FilterInvoices(searchText);
        }

        private void FilterInvoices(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                InvoiceComboBox.ItemsSource = AvailableInvoices;
                return;
            }

            // Search in invoice number, supplier name, and display text
            var filteredInvoices = AvailableInvoices.Where(invoice =>
                invoice.InvoiceNumber.ToString().Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (invoice.Supplier?.Name?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                invoice.DisplayText.Contains(searchText, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            InvoiceComboBox.ItemsSource = filteredInvoices;

            // Keep dropdown open if there are results
            if (filteredInvoices.Any() && !InvoiceComboBox.IsDropDownOpen)
            {
                InvoiceComboBox.IsDropDownOpen = true;
            }
        }

        private void BrowseAttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "اختر ملف الوصل",
                Filter = "ملفات الصور|*.jpg;*.jpeg;*.png;*.bmp;*.gif|ملفات PDF|*.pdf|جميع الملفات|*.*",
                FilterIndex = 1
            };
            
            if (openFileDialog.ShowDialog() == true)
            {
                AttachmentPath = openFileDialog.FileName;
                AttachmentPathTextBox.Text = Path.GetFileName(AttachmentPath);
                OnPropertyChanged(nameof(AttachmentPath));
            }
        }
        
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm()) return;
            
            try
            {
                SaveButton.IsEnabled = false;
                
                // Play save animation
                var saveAnimation = (Storyboard)Resources["SaveSuccessAnimation"];
                saveAnimation.Begin();
                
                var payment = CreatePaymentFromForm();
                
                if (_isEditMode && _currentPayment != null)
                {
                    payment.Id = _currentPayment.Id;
                    await _paymentService.UpdatePaymentAsync(payment);
                    ShowSuccessToastWithAutoClose("✅ تم تحديث وصل الدفع بنجاح!");
                }
                else
                {
                    await _paymentService.CreatePaymentAsync(payment);
                    ShowSuccessToastWithAutoClose("🎉 تم إضافة وصل الدفع بنجاح!");

                    // Reload available invoices to update remaining amounts
                    await LoadAvailableInvoicesAsync();
                }

                PaymentSaved?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في حفظ الوصل", ex.Message);
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }
        
        private bool ValidateForm()
        {
            if (_selectedInvoice == null)
            {
                _toastService.ShowWarning("تحذير", "يرجى اختيار الفاتورة");
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(ReceiptNumberTextBox.Text))
            {
                _toastService.ShowWarning("تحذير", "يرجى إدخال رقم الوصل");
                return false;
            }
            
            if (!decimal.TryParse(PaymentAmountTextBox.Text, out decimal amount) || amount <= 0)
            {
                _toastService.ShowWarning("تحذير", "يرجى إدخال مبلغ صحيح");
                return false;
            }
            
            if (amount > _selectedInvoice.RemainingAmount && !_isEditMode)
            {
                _toastService.ShowWarning("تحذير", $"مبلغ الدفعة لا يمكن أن يتجاوز المبلغ المتبقي ({_selectedInvoice.RemainingAmount:N0} د.ع)");
                return false;
            }
            
            if (PaymentDatePicker.SelectedDate == null)
            {
                _toastService.ShowWarning("تحذير", "يرجى اختيار تاريخ الدفع");
                return false;
            }

            if (PaymentStatusComboBox.SelectedIndex == -1)
            {
                _toastService.ShowWarning("تحذير", "يرجى تحديد حالة التسديد");
                return false;
            }

            // التحقق من الخصم إذا كانت الحالة "تسديد وبخصم"
            var selectedStatus = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
            if (selectedStatus?.Tag?.ToString() == "PaymentWithDiscount")
            {
                if (!decimal.TryParse(DiscountAmountTextBox.Text, out decimal discountAmount) || discountAmount <= 0)
                {
                    _toastService.ShowWarning("تحذير", "يرجى إدخال مبلغ خصم صحيح");
                    return false;
                }

                decimal totalPaidAndDiscount = amount + discountAmount;
                if (totalPaidAndDiscount < _selectedInvoice.Amount)
                {
                    _toastService.ShowWarning("تحذير", $"مجموع المبلغ المسدد والخصم ({totalPaidAndDiscount:N0} د.ع) أقل من قيمة الفاتورة ({_selectedInvoice.Amount:N0} د.ع)");
                    return false;
                }

                if (totalPaidAndDiscount > _selectedInvoice.Amount * 1.1m) // السماح بتجاوز 10% كحد أقصى
                {
                    _toastService.ShowWarning("تحذير", $"مجموع المبلغ المسدد والخصم ({totalPaidAndDiscount:N0} د.ع) يتجاوز قيمة الفاتورة بشكل مفرط");
                    return false;
                }
            }

            return true;
        }
        
        private Payment CreatePaymentFromForm()
        {
            var selectedMethod = PaymentMethodComboBox.SelectedItem as ComboBoxItem;
            var methodTag = selectedMethod?.Tag?.ToString() ?? "Cash";

            var paymentAmount = decimal.Parse(PaymentAmountTextBox.Text);
            var discountAmount = decimal.TryParse(DiscountAmountTextBox.Text, out decimal discount) ? discount : 0;

            // Get payment status
            var selectedStatus = PaymentStatusComboBox.SelectedItem as ComboBoxItem;
            var statusTag = selectedStatus?.Tag?.ToString() ?? "FullyPaid";
            var paymentStatus = statusTag switch
            {
                "PartiallyPaid" => PaymentStatus.PartialPayment,
                "PaymentWithDiscount" => PaymentStatus.PaymentWithDiscount,
                _ => PaymentStatus.FullPayment
            };

            // Update invoice status based on payment amount and discount
            UpdateInvoiceStatus(paymentAmount, discountAmount, paymentStatus);

            return new Payment
            {
                InvoiceId = _selectedInvoice!.Id,
                ReceiptNumber = ReceiptNumberTextBox.Text.Trim(),
                Amount = paymentAmount,
                DiscountAmount = discountAmount,
                PaymentDate = PaymentDatePicker.SelectedDate!.Value,
                Method = Enum.Parse<PaymentMethod>(methodTag),
                Status = paymentStatus,
                Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim(),
                AttachmentPath = string.IsNullOrWhiteSpace(AttachmentPath) ? null : AttachmentPath,
                CreatedDate = DateTime.Now,
                UpdatedDate = _isEditMode ? DateTime.Now : null,
                IsActive = true
            };
        }

        private async void UpdateInvoiceStatus(decimal paymentAmount, decimal discountAmount, PaymentStatus paymentStatus)
        {
            if (_selectedInvoice == null) return;

            try
            {
                // Calculate new paid amount and total with discount
                decimal newPaidAmount = _selectedInvoice.CalculatedPaidAmount + paymentAmount;
                decimal totalDiscountAmount = _selectedInvoice.TotalDiscountAmount + discountAmount;
                decimal totalPaidAndDiscount = newPaidAmount + totalDiscountAmount;

                // Update invoice status based on payment type
                if (paymentStatus == PaymentStatus.PaymentWithDiscount && totalPaidAndDiscount >= _selectedInvoice.Amount)
                {
                    _selectedInvoice.Status = InvoiceStatus.PaidWithDiscount;
                }
                else if (totalPaidAndDiscount >= _selectedInvoice.Amount)
                {
                    _selectedInvoice.Status = InvoiceStatus.Paid;
                }
                else if (newPaidAmount > 0 || totalDiscountAmount > 0)
                {
                    _selectedInvoice.Status = InvoiceStatus.PartiallyPaid;
                }
                else
                {
                    _selectedInvoice.Status = InvoiceStatus.Unpaid;
                }

                // Update stored paid amount
                _selectedInvoice.PaidAmount = newPaidAmount;

                // Update invoice in database
                await _invoiceService.UpdateInvoiceAsync(_selectedInvoice);
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحديث حالة الفاتورة", ex.Message);
            }
        }
        
        private void ShowSuccessToast(string message)
        {
            // Find parent grid to add notification
            var parentGrid = this.Parent as Grid;
            if (parentGrid != null)
            {
                var successNotification = new EnhancedSuccessNotification();

                // Set up the notification
                successNotification.Title = "تم حفظ الدفعة بنجاح";
                successNotification.Message = message;
                successNotification.Icon = "✓";
                successNotification.ShowActions = true;
                successNotification.PrimaryActionText = "عرض الفاتورة";
                successNotification.SecondaryActionText = "إضافة دفعة أخرى";

                // Set up event handlers for notification actions
                successNotification.PrimaryActionClicked += (s, e) =>
                {
                    parentGrid.Children.Remove(successNotification);
                    // Navigate to invoice details if needed
                };

                successNotification.SecondaryActionClicked += (s, e) =>
                {
                    parentGrid.Children.Remove(successNotification);
                    ResetForm();
                };

                // Add to parent grid
                parentGrid.Children.Add(successNotification);
            }
        }

        private void ShowSuccessToastWithAutoClose(string message)
        {
            // Find parent grid to add notification
            var parentGrid = this.Parent as Grid;
            if (parentGrid != null)
            {
                var successNotification = new EnhancedSuccessNotification();

                // إغلاق النافذة تلقائياً بعد 4 ثوان (وقت كافي لقراءة الرسالة)
                var autoCloseTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(4)
                };

                // Set up the notification
                successNotification.Title = "تم حفظ الدفعة بنجاح";
                successNotification.Message = message;
                successNotification.Icon = "✓";
                successNotification.ShowActions = true;
                successNotification.PrimaryActionText = "عرض الفاتورة";
                successNotification.SecondaryActionText = "إضافة دفعة أخرى";

                // Set up event handlers for notification actions
                successNotification.PrimaryActionClicked += (s, e) =>
                {
                    autoCloseTimer.Stop(); // إيقاف المؤقت
                    parentGrid.Children.Remove(successNotification);
                    CloseForm(); // إغلاق النافذة عند النقر على عرض الفاتورة
                };

                successNotification.SecondaryActionClicked += (s, e) =>
                {
                    autoCloseTimer.Stop(); // إيقاف المؤقت التلقائي
                    parentGrid.Children.Remove(successNotification);
                    ResetForm(); // إعادة تعيين النموذج دون إغلاق النافذة
                };

                // Add to parent grid
                parentGrid.Children.Add(successNotification);

                autoCloseTimer.Tick += (s, e) =>
                {
                    autoCloseTimer.Stop();
                    parentGrid.Children.Remove(successNotification);
                    CloseForm();
                };
                autoCloseTimer.Start();
            }
        }
        
        private void ResetForm()
        {
            ReceiptNumber = string.Empty;
            PaymentAmount = 0;
            DiscountAmount = 0;
            PaymentDate = DateTime.Now;
            SelectedPaymentMethod = PaymentMethod.Cash;
            SelectedPaymentStatus = PaymentStatus.FullPayment;

            Notes = string.Empty;
            AttachmentPath = string.Empty;

            ReceiptNumberTextBox.Text = string.Empty;
            PaymentAmountTextBox.Text = string.Empty;
            DiscountAmountTextBox.Text = string.Empty;
            DiscountAmountTextBox.Visibility = Visibility.Collapsed;
            PaymentDatePicker.SelectedDate = DateTime.Now;
            PaymentMethodComboBox.SelectedIndex = 0;
            PaymentStatusComboBox.SelectedIndex = -1;

            NotesTextBox.Text = string.Empty;
            AttachmentPathTextBox.Text = string.Empty;
            InvoiceComboBox.SelectedItem = null;
            InvoiceInfoPanel.Visibility = Visibility.Collapsed;
            StatusIndicatorPanel.Visibility = Visibility.Collapsed;

            SaveButtonText.Text = "حفظ الوصل";
        }
        
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CloseForm();
        }
        
        private void PaymentStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var tag = selectedItem.Tag?.ToString();
                string status = selectedItem.Content?.ToString() ?? "";

                // إظهار أو إخفاء حقل الخصم حسب الحالة المختارة
                if (tag == "PaymentWithDiscount")
                {
                    DiscountAmountTextBox.Visibility = Visibility.Visible;
                    SelectedPaymentStatus = PaymentStatus.PaymentWithDiscount;
                }
                else
                {
                    DiscountAmountTextBox.Visibility = Visibility.Collapsed;
                    DiscountAmount = 0;
                    DiscountAmountTextBox.Text = string.Empty;

                    if (tag == "FullyPaid")
                        SelectedPaymentStatus = PaymentStatus.FullPayment;
                    else
                        SelectedPaymentStatus = PaymentStatus.PartialPayment;
                }

                // التحقق من صحة المبلغ المدخل مع الحالة المختارة
                if (!string.IsNullOrWhiteSpace(PaymentAmountTextBox.Text) &&
                    decimal.TryParse(PaymentAmountTextBox.Text, out decimal paymentAmount))
                {
                    ValidatePaymentAmount(paymentAmount);
                }
                else
                {
                    // عرض إرشادات للحالة المختارة
                    ShowStatusGuidance(status);
                }

                UpdatePaymentStatus();
            }
        }

        private void ShowStatusGuidance(string status)
        {
            if (_selectedInvoice == null) return;

            switch (status)
            {
                case "تسديد جزئي":
                    ShowStatusIndicator($"💡 أدخل مبلغاً أقل من {_selectedInvoice.RemainingAmount:F0} د.ع", "#2196F3", "Lightbulb");
                    break;
                case "تسديد كامل":
                    ShowStatusIndicator($"💡 أدخل {_selectedInvoice.RemainingAmount:F0} د.ع أو أكثر", "#2196F3", "Lightbulb");
                    break;
                case "تسديد وبخصم":
                    ShowStatusIndicator("💡 أدخل المبلغ المدفوع أو مبلغ الخصم", "#2196F3", "Lightbulb");
                    break;
            }
        }

        private void UpdatePaymentStatus()
        {
            if (_selectedInvoice == null)
                return;

            decimal totalPaidAndDiscount = PaymentAmount + DiscountAmount;
            decimal invoiceAmount = _selectedInvoice.Amount;

            if (SelectedPaymentStatus == PaymentStatus.PaymentWithDiscount)
            {
                if (totalPaidAndDiscount >= invoiceAmount)
                {
                    ShowStatusIndicator($"تسديد وبخصم - المبلغ: {PaymentAmount:F0} د.ع، الخصم: {DiscountAmount:F0} د.ع", "#9C27B0", "CheckCircle");
                }
                else
                {
                    decimal remaining = invoiceAmount - totalPaidAndDiscount;
                    ShowStatusIndicator($"المبلغ + الخصم أقل من قيمة الفاتورة - المتبقي: {remaining:F0} د.ع", "#FF5722", "AlertCircle");
                }
            }
        }

        private void DiscountAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // تجنب التحديث المتكرر أثناء التحديث التلقائي
            if (_isUpdatingAutomatically)
                return;

            if (sender is TextBox textBox && _selectedInvoice != null)
            {
                if (decimal.TryParse(textBox.Text, out decimal discount))
                {
                    DiscountAmount = discount;
                    decimal invoiceAmount = _selectedInvoice.Amount;

                    if (discount < 0)
                    {
                        ShowStatusIndicator("⚠️ مبلغ الخصم لا يمكن أن يكون سالباً", "#FF5722", "AlertCircle");
                    }
                    else if (discount >= invoiceAmount)
                    {
                        ShowStatusIndicator($"⚠️ مبلغ الخصم لا يمكن أن يساوي أو يتجاوز قيمة الفاتورة", "#FF5722", "AlertCircle");
                    }
                    else
                    {
                        // حساب المبلغ المدفوع تلقائياً = مبلغ الفاتورة - مبلغ الخصم
                        decimal calculatedPaymentAmount = invoiceAmount - discount;

                        // تحديث المبلغ المدفوع في الحقل مع تجنب التحديث المتكرر
                        _isUpdatingAutomatically = true;
                        PaymentAmount = calculatedPaymentAmount;
                        PaymentAmountTextBox.Text = calculatedPaymentAmount.ToString("F0");
                        _isUpdatingAutomatically = false;

                        // تأكد من أن حالة الدفع هي "تسديد وبخصم"
                        PaymentStatusComboBox.SelectedIndex = 2; // تسديد وبخصم

                        // عرض معلومات الخصم
                        decimal discountPercentage = (discount / invoiceAmount) * 100;
                        ShowStatusIndicator($"✅ تسديد وبخصم {discountPercentage:F1}% - المبلغ المدفوع: {calculatedPaymentAmount:F0} د.ع", "#9C27B0", "CheckCircle");
                    }

                    UpdatePaymentStatus();
                }
                else
                {
                    DiscountAmount = 0;
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CloseForm();
        }
        
        private void CloseForm()
        {
            var slideOutStoryboard = (Storyboard)Resources["SlideOutAnimation"];
            slideOutStoryboard.Begin();
        }
        
        private void SlideOutAnimation_Completed(object sender, EventArgs e)
        {
            FormClosed?.Invoke(this, EventArgs.Empty);
        }
        
        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
