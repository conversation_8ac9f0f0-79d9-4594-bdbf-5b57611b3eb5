<Window x:Class="HR_InvoiceArchiver.Windows.AddEditInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:HR_InvoiceArchiver.Converters"
        Title="إضافة/تعديل فاتورة"
        Height="800" Width="1000"
        MinHeight="700" MinWidth="900"
        FlowDirection="RightToLeft"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="#F1F5F9">

    <Window.Resources>
        <converters:StatusToTextConverter x:Key="StatusToTextConverter"/>
        <converters:CurrencyConverter x:Key="CurrencyConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Enhanced Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="30"/>
            <Setter Property="Margin" Value="25,15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="15" ShadowDepth="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Enhanced Input Style -->
        <Style x:Key="ModernInputStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Height" Value="55"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#6366F1"/>
            <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
        </Style>

        <!-- Enhanced ComboBox Style -->
        <Style x:Key="ModernComboStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Height" Value="55"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>

        <!-- Enhanced DatePicker Style -->
        <Style x:Key="ModernDateStyle" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignOutlinedDatePicker}">
            <Setter Property="Height" Value="55"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Enhanced Icon and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="FileDocumentPlus" Width="32" Height="32"
                                           VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock x:Name="WindowTitleTextBlock" Text="إضافة فاتورة جديدة"
                                  FontSize="24" FontWeight="Bold"/>
                        <TextBlock Text="إدخال بيانات الفاتورة الجديدة"
                                  FontSize="14" Opacity="0.8"/>
                    </StackPanel>
                </StackPanel>

                <!-- Close Button -->
                <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}"
                        Click="CloseButton_Click" ToolTip="إغلاق">
                    <materialDesign:PackIcon Kind="Close" Width="24" Height="24"/>
                </Button>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Enhanced Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="0,10,0,0">
            <StackPanel>

                <!-- Basic Information Card -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- Section Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,25">
                            <materialDesign:PackIcon Kind="InformationOutline" Width="28" Height="28"
                                                   Foreground="#3B82F6" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="المعلومات الأساسية" FontSize="20" FontWeight="Bold" Foreground="#1E293B"/>
                                <TextBlock Text="بيانات الفاتورة الرئيسية" FontSize="14" Foreground="#64748B"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="25"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Invoice Number -->
                            <TextBox x:Name="InvoiceNumberTextBox" Grid.Column="0"
                                    Style="{StaticResource ModernInputStyle}"
                                    materialDesign:HintAssist.Hint="رقم الفاتورة *"
                                    Text="{Binding InvoiceNumber, UpdateSourceTrigger=PropertyChanged}"/>

                            <!-- Invoice Date -->
                            <DatePicker x:Name="InvoiceDatePicker" Grid.Column="2"
                                       Style="{StaticResource ModernDateStyle}"
                                       materialDesign:HintAssist.Hint="تاريخ الفاتورة *"
                                       SelectedDate="{Binding InvoiceDate, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <!-- Supplier -->
                        <ComboBox x:Name="SupplierComboBox"
                                 Style="{StaticResource ModernComboStyle}"
                                 materialDesign:HintAssist.Hint="المورد *"
                                 ItemsSource="{Binding Suppliers}"
                                 SelectedItem="{Binding SelectedSupplier, UpdateSourceTrigger=PropertyChanged}"
                                 DisplayMemberPath="Name"
                                 SelectedValuePath="Id"/>

                        <!-- Description -->
                        <TextBox x:Name="DescriptionTextBox"
                                Style="{StaticResource ModernInputStyle}"
                                materialDesign:HintAssist.Hint="وصف الفاتورة"
                                Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                AcceptsReturn="True"
                                TextWrapping="Wrap"
                                MinLines="3"
                                MaxLines="5"
                                Height="90"/>
                    </StackPanel>
                </Border>

                <!-- Financial Information Card -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- Section Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,25">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28"
                                                   Foreground="#10B981" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="المعلومات المالية" FontSize="20" FontWeight="Bold" Foreground="#1E293B"/>
                                <TextBlock Text="المبالغ والحالة المالية للفاتورة" FontSize="14" Foreground="#64748B"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="25"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Total Amount -->
                            <TextBox x:Name="TotalAmountTextBox" Grid.Column="0"
                                    Style="{StaticResource ModernInputStyle}"
                                    materialDesign:HintAssist.Hint="المبلغ الإجمالي (د.ع) *"
                                    Text="{Binding TotalAmount, UpdateSourceTrigger=PropertyChanged}"/>

                            <!-- Due Date -->
                            <DatePicker x:Name="DueDatePicker" Grid.Column="2"
                                       Style="{StaticResource ModernDateStyle}"
                                       materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                                       SelectedDate="{Binding DueDate, UpdateSourceTrigger=PropertyChanged}"/>
                        </Grid>

                        <!-- Status -->
                        <ComboBox x:Name="StatusComboBox"
                                 Style="{StaticResource ModernComboStyle}"
                                 materialDesign:HintAssist.Hint="حالة الفاتورة *"
                                 SelectedItem="{Binding Status, UpdateSourceTrigger=PropertyChanged}">
                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Converter={StaticResource StatusToTextConverter}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                    </StackPanel>
                </Border>

                <!-- Attachments Card -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <!-- Section Header -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,25">
                            <materialDesign:PackIcon Kind="Attachment" Width="28" Height="28"
                                                   Foreground="#F59E0B" VerticalAlignment="Center" Margin="0,0,15,0"/>
                            <StackPanel VerticalAlignment="Center">
                                <TextBlock Text="المرفقات" FontSize="20" FontWeight="Bold" Foreground="#1E293B"/>
                                <TextBlock Text="إرفاق ملفات مع الفاتورة" FontSize="14" Foreground="#64748B"/>
                            </StackPanel>
                        </StackPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="AttachmentPathTextBox" Grid.Column="0"
                                    Style="{StaticResource ModernInputStyle}"
                                    materialDesign:HintAssist.Hint="مسار المرفق"
                                    Text="{Binding AttachmentPath, UpdateSourceTrigger=PropertyChanged}"
                                    IsReadOnly="True"/>

                            <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}"
                                   Content="تصفح" Click="BrowseAttachmentButton_Click"
                                   Margin="15,12,0,12" Padding="20,12" Height="55">
                                <Button.CommandParameter>
                                    <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18" Margin="0,0,8,0"/>
                                </Button.CommandParameter>
                            </Button>
                        </Grid>

                        <!-- Attachment Preview -->
                        <Border x:Name="AttachmentPreviewBorder" Background="#F8F9FA"
                               CornerRadius="8" Padding="15" Margin="0,15,0,0"
                               Visibility="{Binding HasAttachment, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0" Kind="FileDocument"
                                                       Width="24" Height="24" VerticalAlignment="Center"
                                                       Margin="0,0,12,0" Foreground="#6366F1"/>

                                <TextBlock x:Name="AttachmentNameTextBlock" Grid.Column="1"
                                          Text="{Binding AttachmentName}"
                                          VerticalAlignment="Center" TextTrimming="CharacterEllipsis"
                                          FontSize="16"/>

                                <Button Grid.Column="2" Style="{StaticResource MaterialDesignIconButton}"
                                       Click="RemoveAttachmentButton_Click" ToolTip="إزالة المرفق">
                                    <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                                </Button>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <materialDesign:Card Grid.Row="2" Margin="25,15" Padding="25"
                            materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Validation Messages -->
                <TextBlock x:Name="ValidationMessageTextBlock" Grid.Column="0"
                          Text="" Foreground="Red" VerticalAlignment="Center"
                          TextWrapping="Wrap" Visibility="Collapsed" FontSize="14"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1" Style="{StaticResource MaterialDesignOutlinedButton}"
                       Content="إلغاء" Click="CancelButton_Click"
                       Margin="0,0,15,0" Padding="30,12" MinWidth="120" Height="50"
                       FontSize="16">
                    <Button.CommandParameter>
                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18" Margin="0,0,8,0"/>
                    </Button.CommandParameter>
                </Button>

                <!-- Save Button -->
                <Button x:Name="SaveButton" Grid.Column="2" Style="{StaticResource MaterialDesignRaisedButton}"
                       Content="حفظ الفاتورة" Click="SaveButton_Click"
                       Padding="30,12" MinWidth="150" Height="50" FontSize="16"
                       Background="#10B981">
                    <Button.CommandParameter>
                        <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20" Margin="0,0,8,0"/>
                    </Button.CommandParameter>
                </Button>
            </Grid>
        </materialDesign:Card>

    </Grid>
</Window>
