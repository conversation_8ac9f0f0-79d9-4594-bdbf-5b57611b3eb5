using Microsoft.EntityFrameworkCore;
using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    /// <summary>
    /// مستودع العروض - نسخة مبسطة
    /// </summary>
    public class OfferRepository : IOfferRepository
    {
        private readonly DatabaseContext _context;

        public OfferRepository(DatabaseContext context)
        {
            _context = context;
        }

        // العمليات الأساسية
        public async Task<IEnumerable<Offer>> GetAllAsync()
        {
            try
            {
                return await _context.Offers
                    .OrderByDescending(o => o.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<Offer>();
            }
        }

        public async Task<IEnumerable<Offer>> GetActiveOffersAsync()
        {
            try
            {
                return await _context.Offers
                    .Where(o => o.IsActive)
                    .OrderByDescending(o => o.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<Offer>();
            }
        }

        public async Task<Offer?> GetByIdAsync(int id)
        {
            try
            {
                return await _context.Offers
                    .FirstOrDefaultAsync(o => o.Id == id);
            }
            catch (Exception)
            {
                return null;
            }
        }

        public async Task<Offer> CreateAsync(Offer offer)
        {
            try
            {
                offer.CreatedAt = DateTime.Now;
                _context.Offers.Add(offer);
                await _context.SaveChangesAsync();
                return offer;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<Offer> UpdateAsync(Offer offer)
        {
            try
            {
                offer.UpdatedAt = DateTime.Now;
                _context.Offers.Update(offer);
                await _context.SaveChangesAsync();
                return offer;
            }
            catch (Exception)
            {
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var offer = await GetByIdAsync(id);
                if (offer == null) return false;

                _context.Offers.Remove(offer);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> SoftDeleteAsync(int id)
        {
            try
            {
                var offer = await GetByIdAsync(id);
                if (offer == null) return false;

                offer.IsActive = false;
                offer.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                return await _context.Offers.AnyAsync(o => o.Id == id);
            }
            catch (Exception)
            {
                return false;
            }
        }

        // البحث الأساسي
        public async Task<IEnumerable<Offer>> SearchAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetActiveOffersAsync();

                var term = searchTerm.Trim().ToLower();
                return await _context.Offers
                    .Where(o => o.IsActive && (
                        o.ScientificName.ToLower().Contains(term) ||
                        o.ScientificOffice.ToLower().Contains(term) ||
                        o.RepresentativeName.ToLower().Contains(term) ||
                        (o.TradeName != null && o.TradeName.ToLower().Contains(term))
                    ))
                    .OrderByDescending(o => o.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<Offer>();
            }
        }

        public async Task<IEnumerable<Offer>> GetOffersByScientificNameAsync(string scientificName)
        {
            try
            {
                return await _context.Offers
                    .Where(o => o.IsActive && o.ScientificName.ToLower() == scientificName.ToLower())
                    .OrderBy(o => o.Price)
                    .ToListAsync();
            }
            catch (Exception)
            {
                return new List<Offer>();
            }
        }

        // إحصائيات أساسية
        public async Task<int> GetTotalOffersCountAsync()
        {
            try
            {
                return await _context.Offers.CountAsync();
            }
            catch (Exception)
            {
                return 0;
            }
        }

        public async Task<int> GetActiveOffersCountAsync()
        {
            try
            {
                return await _context.Offers.CountAsync(o => o.IsActive);
            }
            catch (Exception)
            {
                return 0;
            }
        }
    }
}
