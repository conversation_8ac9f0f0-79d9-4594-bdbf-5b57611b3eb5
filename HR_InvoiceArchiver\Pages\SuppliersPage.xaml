<UserControl x:Class="HR_InvoiceArchiver.Pages.SuppliersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا (ModernCardStyle, StatCardStyle, ModernPrimaryButtonStyle, ModernSecondaryButtonStyle, ModernSpecialButtonStyle) -->
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Modern Header -->
        <Border Grid.Row="0" Margin="15" CornerRadius="25" Padding="35">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667EEA" Offset="0"/>
                    <GradientStop Color="#764BA2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#667EEA" Opacity="0.3" BlurRadius="25" ShadowDepth="8"/>
            </Border.Effect>

            <Grid>
                <!-- Title and Actions Row -->
                <Grid Margin="0,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Section - Enhanced Icon -->
                    <Border Grid.Column="0" Background="#26FFFFFF" CornerRadius="20"
                           Width="85" Height="85" Margin="0,0,25,0">
                        <materialDesign:PackIcon Kind="AccountMultiple" Width="45" Height="45"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <!-- Center Section - Enhanced Title -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="إدارة الموردين" FontSize="32" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="نظام شامل ومتطور لإدارة الموردين والحسابات" FontSize="16"
                                 Foreground="#D9FFFFFF" Margin="0,8,0,0"/>
                    </StackPanel>

                    <!-- Right Section - Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Add Supplier Button -->
                        <Border Background="#10B981" CornerRadius="15" Margin="8,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#10B981" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                            </Border.Effect>
                            <Button x:Name="AddNewSupplierButton" Click="AddNewSupplierButton_Click"
                                   Background="Transparent" Foreground="White" BorderThickness="0"
                                   MinWidth="140" Height="45" FontSize="14" FontWeight="SemiBold">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="إضافة مورد" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Border>

                        <!-- Statement Button -->
                        <Border Background="#3B82F6" CornerRadius="15" Margin="8,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#3B82F6" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                            </Border.Effect>
                            <Button x:Name="SupplierStatementButton" Click="SupplierStatementButton_Click"
                                   Background="Transparent" Foreground="White" BorderThickness="0"
                                   MinWidth="120" Height="45" FontSize="14" FontWeight="SemiBold">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocumentEdit" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="كشف حساب" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Border>

                        <!-- Refresh Button -->
                        <Border Background="#8B5CF6" CornerRadius="15" Margin="8,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#8B5CF6" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                            </Border.Effect>
                            <Button x:Name="RefreshButton" Click="RefreshButton_Click"
                                   Background="Transparent" Foreground="White" BorderThickness="0"
                                   MinWidth="100" Height="45" FontSize="14" FontWeight="SemiBold">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Border>

                        <!-- Export Button -->
                        <Border Background="#F59E0B" CornerRadius="15" Margin="8,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#F59E0B" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                            </Border.Effect>
                            <Button x:Name="ExportButton" Click="ExportButton_Click"
                                   Background="Transparent" Foreground="White" BorderThickness="0"
                                   MinWidth="100" Height="45" FontSize="14" FontWeight="SemiBold">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExport" Width="18" Height="18" Margin="0,0,8,0"/>
                                    <TextBlock Text="تصدير" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Border>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- Enhanced Statistics Cards Section -->
        <Grid Grid.Row="1" Margin="15,0,15,15">
            <UniformGrid Columns="4" Margin="0">
                    <!-- Total Suppliers Card -->
                    <Border Background="White" CornerRadius="18" Padding="20" Margin="8">
                        <Border.Effect>
                            <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Header with Icon -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <Border Background="#E3F2FD" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                                    <materialDesign:PackIcon Kind="AccountMultiple" Width="22" Height="22"
                                                           Foreground="#1976D2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إجمالي الموردين" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                                    <TextBlock Text="Total Suppliers" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1" x:Name="TotalSuppliersText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#1976D2" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                            <!-- Footer Badge -->
                            <Border Grid.Row="2" Background="#F0F9FF" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                                <TextBlock Text="مورد مسجل" FontSize="12" Foreground="#1976D2" FontWeight="SemiBold"/>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Active Suppliers Card -->
                    <Border Background="White" CornerRadius="18" Padding="20" Margin="8">
                        <Border.Effect>
                            <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Header with Icon -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <Border Background="#E8F5E8" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                                    <materialDesign:PackIcon Kind="CheckCircleOutline" Width="22" Height="22"
                                                           Foreground="#388E3C" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="الموردين النشطين" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                                    <TextBlock Text="Active Suppliers" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1" x:Name="ActiveSuppliersText" Text="0" FontSize="32" FontWeight="Bold"
                                     Foreground="#388E3C" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                            <!-- Footer Badge -->
                            <Border Grid.Row="2" Background="#F0FDF4" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                                <TextBlock Text="مورد نشط" FontSize="12" Foreground="#388E3C" FontWeight="SemiBold"/>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Total Outstanding Card -->
                    <Border Background="White" CornerRadius="18" Padding="20" Margin="8">
                        <Border.Effect>
                            <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Header with Icon -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <Border Background="#FFF3E0" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                                    <materialDesign:PackIcon Kind="CashMinus" Width="22" Height="22"
                                                           Foreground="#F57C00" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إجمالي المستحقات" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                                    <TextBlock Text="Total Outstanding" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1" x:Name="TotalOutstandingText" Text="0 د.ع" FontSize="32" FontWeight="Bold"
                                     Foreground="#F57C00" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                            <!-- Footer Badge -->
                            <Border Grid.Row="2" Background="#FFFBF0" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                                <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#F57C00" FontWeight="SemiBold"/>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Total Paid Card -->
                    <Border Background="White" CornerRadius="18" Padding="20" Margin="8">
                        <Border.Effect>
                            <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Header with Icon -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                <Border Background="#F3E5F5" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                                    <materialDesign:PackIcon Kind="CashCheck" Width="22" Height="22"
                                                           Foreground="#7B1FA2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel VerticalAlignment="Center">
                                    <TextBlock Text="إجمالي المسدد" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                                    <TextBlock Text="Total Paid" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- Main Value -->
                            <TextBlock Grid.Row="1" x:Name="TotalPaidText" Text="0 د.ع" FontSize="32" FontWeight="Bold"
                                     Foreground="#7B1FA2" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                            <!-- Footer Badge -->
                            <Border Grid.Row="2" Background="#FAF5FF" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                                <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                            </Border>
                        </Grid>
                    </Border>
            </UniformGrid>
        </Grid>

        <!-- Enhanced Modern Main Content -->
        <Border Grid.Row="2" Background="White" CornerRadius="25" Padding="30" Margin="0">
            <Border.Effect>
                <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="30" ShadowDepth="8"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Enhanced Modern Search Section -->
                <Border Grid.Row="0" Background="#F8FAFC" CornerRadius="15" Padding="18" Margin="0,0,0,15">
                    <Border.Effect>
                        <DropShadowEffect Color="#64748B" Opacity="0.05" BlurRadius="15" ShadowDepth="3"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- Main Search Row -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Enhanced Search Box -->
                            <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="18,12" Margin="0,0,20,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#64748B" Opacity="0.08" BlurRadius="12" ShadowDepth="2"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="20" Height="20"
                                                           Foreground="#64748B" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBox Grid.Column="1" x:Name="SearchTextBox" BorderThickness="0" Background="Transparent"
                                            materialDesign:HintAssist.Hint="البحث في الموردين بالاسم، الهاتف، أو العنوان..."
                                            FontSize="15" VerticalAlignment="Center"
                                            TextChanged="SearchTextBox_TextChanged"/>
                                </Grid>
                            </Border>

                            <!-- Action Buttons -->
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Border Background="#EF4444" CornerRadius="12" Margin="6,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#EF4444" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                    </Border.Effect>
                                    <Button Background="Transparent" Foreground="White" BorderThickness="0"
                                           MinWidth="100" Height="38" FontSize="13" FontWeight="SemiBold"
                                           Click="ClearSearchButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Margin="0,0,6,0"/>
                                            <TextBlock Text="مسح البحث" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </Border>

                                <!-- Quick Filter Buttons -->
                                <Button Background="#10B981" Foreground="White" BorderThickness="0"
                                       MinWidth="100" Height="36" Margin="4,0" ToolTip="عرض الموردين النشطين فقط"
                                       Click="QuickFilterActive_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="12">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#059669"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="نشط" FontSize="11" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <Button Background="#F59E0B" Foreground="White" BorderThickness="0"
                                       MinWidth="100" Height="36" Margin="4,0" ToolTip="عرض الموردين غير النشطين فقط"
                                       Click="QuickFilterInactive_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="12">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#D97706"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CloseCircle" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="غير نشط" FontSize="11" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Enhanced Suppliers DataGrid -->
                <Border Grid.Row="1" Background="White" CornerRadius="18" Padding="5" Margin="0">
                    <Border.Effect>
                        <DropShadowEffect Color="#64748B" Opacity="0.08" BlurRadius="20" ShadowDepth="4"/>
                    </Border.Effect>
                    <DataGrid x:Name="SuppliersDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="None"
                             HeadersVisibility="Column"
                             RowHeight="60"
                             FontSize="14"
                             Background="Transparent"
                             BorderThickness="0"
                             HorizontalScrollBarVisibility="Auto"
                             VerticalScrollBarVisibility="Auto"
                             SelectionChanged="SuppliersDataGrid_SelectionChanged"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             MinHeight="600">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Margin" Value="0,2"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#BBDEFB"/>
                                    <Setter Property="BorderBrush" Value="#007BFF"/>
                                    <Setter Property="BorderThickness" Value="2,0"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#495057"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Height" Value="50"/>
                            <Setter Property="BorderThickness" Value="0,0,0,2"/>
                            <Setter Property="BorderBrush" Value="#DEE2E6"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <!-- ID Column -->
                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="90" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#007BFF"/>
                                    <Setter Property="Background" Value="#E3F2FD"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Name Column -->
                        <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="220" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#212529"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Phone Column -->
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="160" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontSize" Value="15"/>
                                    <Setter Property="Foreground" Value="#6C757D"/>
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Email Column -->
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontSize" Value="14"/>
                                    <Setter Property="Foreground" Value="#6C757D"/>
                                    <Setter Property="FontStyle" Value="Italic"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Invoice Count Column -->
                        <DataGridTextColumn Header="عدد الفواتير" Binding="{Binding InvoiceCount}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#28A745"/>
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Total Amount Column -->
                        <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalAmount, StringFormat='{}{0:N0} د.ع'}" Width="160" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="16"/>
                                    <Setter Property="Foreground" Value="#FD7E14"/>
                                    <Setter Property="Background" Value="#FFF3E0"/>
                                    <Setter Property="Padding" Value="8,4"/>
                                    <Setter Property="TextAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- Enhanced Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="200" CanUserSort="False" CanUserReorder="False">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                        <!-- Edit Button -->
                                        <Border Background="#E8F5E8" CornerRadius="8" Margin="2">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#4CAF50" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                            </Border.Effect>
                                            <Button ToolTip="تعديل المورد"
                                                   Width="32" Height="32"
                                                   Background="Transparent"
                                                   BorderThickness="0"
                                                   Tag="{Binding}"
                                                   Click="EditSupplierButton_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#334CAF50"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"
                                                                       Foreground="#4CAF50"/>
                                            </Button>
                                        </Border>

                                        <!-- View Details Button -->
                                        <Border Background="#E3F2FD" CornerRadius="8" Margin="2">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#2196F3" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                            </Border.Effect>
                                            <Button ToolTip="عرض التفاصيل"
                                                   Width="32" Height="32"
                                                   Background="Transparent"
                                                   BorderThickness="0"
                                                   Tag="{Binding}"
                                                   Click="ViewSupplierDetailsButton_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#332196F3"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"
                                                                       Foreground="#2196F3"/>
                                            </Button>
                                        </Border>

                                        <!-- Delete Button -->
                                        <Border Background="#FFEBEE" CornerRadius="8" Margin="2">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#F44336" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                            </Border.Effect>
                                            <Button ToolTip="حذف المورد"
                                                   Width="32" Height="32"
                                                   Background="Transparent"
                                                   BorderThickness="0"
                                                   Tag="{Binding}"
                                                   Click="DeleteSupplierButton_Click">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#33F44336"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                                <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"
                                                                       Foreground="#F44336"/>
                                            </Button>
                                        </Border>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
                </Border>
            </Grid>
        </Border>

        <!-- Modern Loading Panel -->
        <materialDesign:Card x:Name="LoadingPanel"
                           Grid.Row="1"
                           materialDesign:ElevationAssist.Elevation="Dp8"
                           Background="White"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Padding="40,30">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="50" Height="50"
                           IsIndeterminate="True"
                           Foreground="#007BFF"/>
                <TextBlock Text="جاري تحميل بيانات الموردين..."
                         FontSize="16"
                         FontWeight="Medium"
                         Foreground="#495057"
                         HorizontalAlignment="Center"
                         Margin="0,16,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Empty State Panel -->
        <materialDesign:Card x:Name="EmptyStatePanel"
                           Grid.Row="1"
                           materialDesign:ElevationAssist.Elevation="Dp4"
                           Background="White"
                           Visibility="Collapsed"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Padding="60,40">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="AccountMultipleOutline"
                                       Width="80" Height="80"
                                       Foreground="#CED4DA"
                                       HorizontalAlignment="Center"/>
                <TextBlock Text="لا توجد موردين مسجلين"
                         FontSize="20"
                         FontWeight="SemiBold"
                         Foreground="#6C757D"
                         HorizontalAlignment="Center"
                         Margin="0,20,0,8"/>
                <TextBlock Text="ابدأ بإضافة مورد جديد لعرض البيانات هنا"
                         FontSize="14"
                         Foreground="#ADB5BD"
                         HorizontalAlignment="Center"
                         TextWrapping="Wrap"
                         TextAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
