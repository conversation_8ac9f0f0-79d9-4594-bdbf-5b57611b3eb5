using System.ComponentModel.DataAnnotations;

namespace HR_InvoiceArchiver.Models
{
    /// <summary>
    /// نموذج أسماء المواد العلمية - نسخة مبسطة
    /// </summary>
    public class ScientificName
    {
        /// <summary>
        /// المعرف الفريد
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم المادة العلمية
        /// </summary>
        [Required(ErrorMessage = "اسم المادة العلمية مطلوب")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// وصف المادة العلمية (اختياري)
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// فئة المادة العلمية (اختياري)
        /// </summary>
        [StringLength(50)]
        public string? Category { get; set; }

        /// <summary>
        /// تاريخ الإضافة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// حالة النشاط
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
