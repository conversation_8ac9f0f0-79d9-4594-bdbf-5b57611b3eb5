<?xml version="1.0" encoding="utf-8"?>
<UserControl x:Class="HR_InvoiceArchiver.Pages.ChartsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft"
             FontFamily="{DynamicResource MaterialDesignFont}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             Background="#F8F9FF">

    <UserControl.Resources>
        <ResourceDictionary>
            <!-- Modern Card Style -->
            <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp6"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" BlurRadius="20" ShadowDepth="8" Direction="270"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Enhanced Gradient Brushes -->
            <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#10B981" Offset="0"/>
                <GradientStop Color="#059669" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="WarningGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#FF6B6B" Offset="0"/>
                <GradientStop Color="#EE5A52" Offset="1"/>
            </LinearGradientBrush>

            <LinearGradientBrush x:Key="ErrorGradientBrush" StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#8B5CF6" Offset="0"/>
                <GradientStop Color="#7C3AED" Offset="1"/>
            </LinearGradientBrush>

            <!-- Chart Button Styles -->
            <Style x:Key="ChartButtonStyle" TargetType="Border">
                <Setter Property="Background" Value="White"/>
                <Setter Property="CornerRadius" Value="12"/>
                <Setter Property="Margin" Value="8,0,0,0"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Opacity" Value="0.7"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#40000000" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Opacity" Value="0.9"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="#40000000" Opacity="0.25" BlurRadius="20" ShadowDepth="8"/>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="SelectedChartButtonStyle" TargetType="Border" BasedOn="{StaticResource ChartButtonStyle}">
                <Setter Property="Opacity" Value="1.0"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Main Container Grid -->
    <Grid>
        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Background="#80FFFFFF" Visibility="Collapsed" Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="LoadingIcon" Kind="Loading" Width="48" Height="48"
                                       Foreground="{StaticResource PrimaryGradientBrush}">
                    <materialDesign:PackIcon.RenderTransform>
                        <RotateTransform Angle="0"/>
                    </materialDesign:PackIcon.RenderTransform>
                </materialDesign:PackIcon>
                <TextBlock Text="جاري تحميل المخططات..." Margin="0,16,0,0"
                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                         HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Main Content -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <Grid Margin="24" x:Name="MainContent">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Enhanced Header -->
                <Border Grid.Row="0" Background="#667eea" CornerRadius="16" Margin="0,0,0,24">
                    <Border.Effect>
                        <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
                    </Border.Effect>
                    
                    <Grid Margin="32,24">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#FFFFFF20" CornerRadius="32" Width="64" Height="64" 
                                VerticalAlignment="Center" Margin="0,0,24,0">
                            <materialDesign:PackIcon Kind="ChartLine" Width="32" Height="32"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="المخططات البيانية والتحليلات المتقدمة"
                                     FontSize="28" FontWeight="Bold" Foreground="White"/>
                            <TextBlock Text="تحليل شامل ومرئي للبيانات المالية مع رؤى ذكية ومخططات تفاعلية"
                                     FontSize="16" Foreground="#E0E7FF" Margin="0,8,0,0"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                            <Button x:Name="RefreshChartsButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="تحديث المخططات" Click="RefreshButton_Click"
                                  Width="48" Height="48" Margin="0,0,8,0">
                                <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                            </Button>
                            <Button x:Name="ExportAllButton" Style="{StaticResource MaterialDesignIconButton}"
                                  Foreground="White" ToolTip="تصدير جميع المخططات" Click="ExportButton_Click"
                                  Width="48" Height="48">
                                <materialDesign:PackIcon Kind="Download" Width="24" Height="24"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Chart Type Selection -->
                <Grid Grid.Row="1" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Monthly Trends Button -->
                    <Border Grid.Column="0" Background="White" CornerRadius="12" Margin="0,0,8,0" Cursor="Hand"
                            x:Name="MonthlyTrendsButton" MouseLeftButtonUp="MonthlyTrendsButton_Click">
                        <Border.Effect>
                            <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                        </Border.Effect>
                        <Grid Margin="16,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#667eea" CornerRadius="20" Width="40" Height="40" 
                                    VerticalAlignment="Center" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" Foreground="White" 
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="الاتجاهات الشهرية" FontSize="16" FontWeight="SemiBold" Foreground="#2D3748"/>
                                <TextBlock Text="تحليل الأداء الشهري" FontSize="12" Foreground="#718096" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Status Distribution Button -->
                    <Border Grid.Column="1" Background="White" CornerRadius="12" Margin="4,0,4,0" Cursor="Hand"
                            x:Name="StatusDistributionButton" MouseLeftButtonUp="StatusDistributionButton_Click">
                        <Border.Effect>
                            <DropShadowEffect Color="#10B981" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                        </Border.Effect>
                        <Grid Margin="16,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#10B981" CornerRadius="20" Width="40" Height="40" 
                                    VerticalAlignment="Center" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="ChartPie" Width="20" Height="20" Foreground="White" 
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="توزيع الحالات" FontSize="16" FontWeight="SemiBold" Foreground="#2D3748"/>
                                <TextBlock Text="حالة الفواتير" FontSize="12" Foreground="#718096" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Supplier Analysis Button -->
                    <Border Grid.Column="2" Background="White" CornerRadius="12" Margin="4,0,4,0" Cursor="Hand"
                            x:Name="SupplierAnalysisButton" MouseLeftButtonUp="SupplierAnalysisButton_Click">
                        <Border.Effect>
                            <DropShadowEffect Color="#FF6B6B" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                        </Border.Effect>
                        <Grid Margin="16,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#FF6B6B" CornerRadius="20" Width="40" Height="40" 
                                    VerticalAlignment="Center" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20" Foreground="White" 
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="تحليل الموردين" FontSize="16" FontWeight="SemiBold" Foreground="#2D3748"/>
                                <TextBlock Text="أداء الموردين" FontSize="12" Foreground="#718096" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Payment Analysis Button -->
                    <Border Grid.Column="3" Background="White" CornerRadius="12" Margin="8,0,0,0" Cursor="Hand"
                            x:Name="PaymentAnalysisButton" MouseLeftButtonUp="PaymentAnalysisButton_Click">
                        <Border.Effect>
                            <DropShadowEffect Color="#8B5CF6" Opacity="0.15" BlurRadius="15" ShadowDepth="5"/>
                        </Border.Effect>
                        <Grid Margin="16,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Background="#8B5CF6" CornerRadius="20" Width="40" Height="40" 
                                    VerticalAlignment="Center" Margin="0,0,12,0">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Foreground="White" 
                                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="تحليل المدفوعات" FontSize="16" FontWeight="SemiBold" Foreground="#2D3748"/>
                                <TextBlock Text="أنماط الدفع" FontSize="12" Foreground="#718096" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!-- Charts Container -->
                <Grid Grid.Row="2">
                    <!-- Main Chart Card -->
                    <materialDesign:Card Style="{StaticResource ModernCardStyle}" x:Name="ChartContainer">
                        <Grid Margin="32">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="400"/>
                            </Grid.RowDefinitions>

                            <!-- Chart Header -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="المخطط البياني"
                                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                             Foreground="{StaticResource PrimaryGradientBrush}" FontWeight="Bold"/>
                                    <TextBlock Text="تحليل تفاعلي للبيانات المالية"
                                             Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                             Opacity="0.7" Margin="0,4,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                          ToolTip="تحديث المخطط" Click="RefreshButton_Click">
                                        <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                    </Button>
                                    <Button x:Name="ExportButton" Style="{StaticResource MaterialDesignIconButton}"
                                          ToolTip="تصدير المخطط" Click="ExportButton_Click" Margin="8,0,0,0">
                                        <materialDesign:PackIcon Kind="Download" Width="20" Height="20"/>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <!-- Chart Controls -->
                            <Grid Grid.Row="1" Margin="0,0,0,16">
                                <TextBlock Text="استخدم الأزرار أعلاه لاختيار نوع المخطط المطلوب"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         Opacity="0.6" HorizontalAlignment="Center"/>
                            </Grid>

                            <!-- Chart Area -->
                            <Border Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}"
                                  CornerRadius="8" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1">
                                <Grid>
                                    <!-- Chart Content Area -->
                                    <Grid x:Name="ChartContentArea">
                                        <!-- Default Message -->
                                        <StackPanel x:Name="DefaultMessage"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center">
                                            <materialDesign:PackIcon Kind="ChartLine"
                                                                   Width="64"
                                                                   Height="64"
                                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                   Margin="0,0,0,16"/>
                                            <TextBlock Text="اختر نوع المخطط من الأزرار أعلاه"
                                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                     HorizontalAlignment="Center"/>
                                            <TextBlock Text="سيتم عرض البيانات هنا بشكل تفاعلي"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                     HorizontalAlignment="Center"
                                                     Margin="0,8,0,0"/>
                                        </StackPanel>

                                        <!-- Chart Data Display -->
                                        <ScrollViewer x:Name="ChartDataDisplay"
                                                    Visibility="Collapsed"
                                                    VerticalScrollBarVisibility="Auto">
                                            <StackPanel x:Name="ChartDataContainer" Margin="16"/>
                                        </ScrollViewer>
                                    </Grid>

                                    <!-- Loading Indicator -->
                                    <Grid x:Name="LoadingIndicator"
                                        Background="#80FFFFFF"
                                        Visibility="Collapsed">
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <ProgressBar IsIndeterminate="True"
                                                       Width="100"
                                                       Height="4"
                                                       Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                                            <TextBlock Text="جاري تحميل البيانات..."
                                                     Margin="0,8,0,0"
                                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </Border>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>
