using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace HR_InvoiceArchiver
{
    /// <summary>
    /// محول لعكس Boolean إلى Visibility
    /// </summary>
    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility != Visibility.Visible;
            }
            return false;
        }
    }

    /// <summary>
    /// محول للتحقق من null وتحويله إلى Visibility
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isInverse = parameter?.ToString() == "Inverse";
            bool isNull = value == null || (value is string str && string.IsNullOrEmpty(str));
            
            if (isInverse)
            {
                return isNull ? Visibility.Collapsed : Visibility.Visible;
            }
            else
            {
                return isNull ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// محول لتحويل القيم الرقمية إلى نص مع تنسيق
    /// </summary>
    public class NumberToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("N2", new CultureInfo("ar-IQ"));
            }
            if (value is double doubleValue)
            {
                return doubleValue.ToString("N2", new CultureInfo("ar-IQ"));
            }
            if (value is int intValue)
            {
                return intValue.ToString("N0", new CultureInfo("ar-IQ"));
            }
            return value?.ToString() ?? "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && !string.IsNullOrEmpty(stringValue))
            {
                if (targetType == typeof(decimal) || targetType == typeof(decimal?))
                {
                    if (decimal.TryParse(stringValue, out decimal result))
                        return result;
                }
                if (targetType == typeof(double) || targetType == typeof(double?))
                {
                    if (double.TryParse(stringValue, out double result))
                        return result;
                }
                if (targetType == typeof(int) || targetType == typeof(int?))
                {
                    if (int.TryParse(stringValue, out int result))
                        return result;
                }
            }
            return null;
        }
    }
}
