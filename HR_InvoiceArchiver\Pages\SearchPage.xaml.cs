using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using HR_InvoiceArchiver.Models;
using HR_InvoiceArchiver.Services;
using HR_InvoiceArchiver.Utils;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.Globalization;

namespace HR_InvoiceArchiver.Pages
{
    public partial class SearchPage : UserControl, INavigationAware
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ISupplierService _supplierService;
        private readonly IToastService _toastService;
        private readonly INavigationService _navigationService;
        private readonly DispatcherTimer _searchTimer;
        private readonly DispatcherTimer _quickSearchTimer;

        // Collections
        public ObservableCollection<Invoice> SearchResults { get; set; } = new();
        public ObservableCollection<Supplier> Suppliers { get; set; } = new();

        // Smart Search Properties
        private List<Invoice> _allInvoices = new();
        private List<string> _searchHistory = new();
        private Dictionary<string, Dictionary<string, object>> _savedSearches = new();
        private int _currentPage = 1;
        private int _itemsPerPage = 100;
        private DateTime _searchStartTime;
        private bool _isAdvancedSearchVisible = true;

        // Statistics Properties
        public int TotalResults { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal OutstandingAmount { get; set; }

        public SearchPage(
            IInvoiceService invoiceService,
            ISupplierService supplierService,
            IToastService toastService,
            INavigationService navigationService)
        {
            _invoiceService = invoiceService;
            _supplierService = supplierService;
            _toastService = toastService;
            _navigationService = navigationService;

            // Initialize timers first
            _searchTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300)
            };

            _quickSearchTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };

            InitializeComponent();

            ResultsDataGrid.ItemsSource = SearchResults;
            SupplierComboBox.ItemsSource = Suppliers;

            InitializeSearchTimers();
            InitializeSmartSearch();
            Loaded += SearchPage_Loaded;
        }

        private void InitializeSearchTimers()
        {
            _searchTimer.Tick += async (s, e) =>
            {
                _searchTimer.Stop();
                await PerformSearchAsync();
            };

            _quickSearchTimer.Tick += async (s, e) =>
            {
                _quickSearchTimer.Stop();
                await PerformQuickSearchAsync();
            };
        }

        private void InitializeSmartSearch()
        {
            // Initialize search history and saved searches
            _searchHistory = new List<string>();
            _savedSearches = new Dictionary<string, Dictionary<string, object>>();

            // Set default values - simplified

            // Update quick stats will be called after data loads
        }

        private async void SearchPage_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
            await LoadQuickStats();
            UpdateEmptyState();
        }

        #region Smart Search Methods

        private async Task PerformQuickSearchAsync()
        {
            try
            {
                _searchStartTime = DateTime.Now;
                ShowLoadingState(true);

                var searchText = QuickSearchTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    SearchResults.Clear();
                    UpdateStatistics();
                    ShowEmptyState(true);
                    return;
                }

                // Add to search history
                AddToSearchHistory(searchText);

                // Perform smart search
                var results = await PerformSmartSearchAsync(searchText);

                // Update UI
                SearchResults.Clear();
                foreach (var invoice in results.Take(_itemsPerPage))
                {
                    SearchResults.Add(invoice);
                }

                UpdateStatistics();
                UpdateSearchTime();
                ShowLoadingState(false);
                ShowEmptyState(SearchResults.Count == 0);

                // Search completed successfully
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في البحث السريع", ex.Message);
                ShowLoadingState(false);
            }
        }

        private async Task<List<Invoice>> PerformSmartSearchAsync(string searchText)
        {
            if (_allInvoices == null || !_allInvoices.Any())
            {
                _allInvoices = (await _invoiceService.GetAllInvoicesAsync()).ToList();
            }

            var results = new List<Invoice>();
            var searchTerms = searchText.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            foreach (var invoice in _allInvoices)
            {
                var score = CalculateSearchScore(invoice, searchTerms);
                if (score > 0)
                {
                    results.Add(invoice);
                }
            }

            // Sort by relevance score
            return results.OrderByDescending(i => CalculateSearchScore(i, searchTerms)).ToList();
        }

        private int CalculateSearchScore(Invoice invoice, string[] searchTerms)
        {
            int score = 0;
            var searchableText = $"{invoice.InvoiceNumber} {invoice.SupplierName} {invoice.Description} {invoice.Amount}".ToLower();

            foreach (var term in searchTerms)
            {
                if (searchableText.Contains(term))
                {
                    score += 10;

                    // Boost score for exact matches in important fields
                    if (invoice.InvoiceNumber.ToLower().Contains(term)) score += 20;
                    if (invoice.SupplierName.ToLower().Contains(term)) score += 15;
                }
            }

            return score;
        }

        private void AddToSearchHistory(string searchText)
        {
            if (!_searchHistory.Contains(searchText))
            {
                _searchHistory.Insert(0, searchText);
                if (_searchHistory.Count > 10) // Keep only last 10 searches
                {
                    _searchHistory.RemoveAt(_searchHistory.Count - 1);
                }
            }
        }

        #endregion

        public void OnNavigatedTo(object parameter)
        {
            // Refresh suppliers when navigating to this page
            Dispatcher.BeginInvoke(new Action(async () => await LoadSuppliersAsync()));
        }

        public void OnNavigatedFrom()
        {
            // Clean up timers
            _searchTimer?.Stop();
            _quickSearchTimer?.Stop();
        }

        #region UI Helper Methods

        private async Task LoadQuickStats()
        {
            try
            {
                if (_allInvoices == null || !_allInvoices.Any())
                {
                    _allInvoices = (await _invoiceService.GetAllInvoicesAsync()).ToList();
                }

                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Update stats if elements exist
                // QuickStatsInvoices.Text = _allInvoices.Count.ToString();
                // QuickStatsSuppliers.Text = suppliers.Count.ToString();
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في تحميل الإحصائيات", ex.Message);
            }
        }

        private void ShowLoadingState(bool isLoading)
        {
            LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
        }

        private void ShowEmptyState(bool isEmpty)
        {
            EmptyStatePanel.Visibility = isEmpty ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = isEmpty ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateSearchTime()
        {
            var elapsed = DateTime.Now - _searchStartTime;
            SearchTimeText.Text = $"{elapsed.TotalMilliseconds:F0}ms";
        }

        private void UpdateFilterStatus()
        {
            var activeFilters = 0;

            if (!string.IsNullOrEmpty(InvoiceNumberTextBox.Text)) activeFilters++;
            if (SupplierComboBox.SelectedItem != null) activeFilters++;
            if (FromDatePicker.SelectedDate.HasValue) activeFilters++;
            if (ToDatePicker.SelectedDate.HasValue) activeFilters++;
            if (!string.IsNullOrEmpty(AmountTextBox.Text)) activeFilters++;
            if (StatusComboBox.SelectedIndex > 0) activeFilters++;

            FilterStatusText.Text = activeFilters > 0 ? $"{activeFilters} فلتر نشط" : "بدون فلاتر";
        }

        #endregion

        private async Task LoadSuppliersAsync()
        {
            try
            {
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                Dispatcher.Invoke(() =>
                {
                    Suppliers.Clear();
                    foreach (var supplier in suppliers)
                    {
                        Suppliers.Add(supplier);
                    }
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في تحميل الموردين", ex.Message);
                });
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformSearchAsync();
        }

        private async Task PerformSearchAsync()
        {
            try
            {
                ShowLoading(true);
                UpdateResultsInfo("جاري البحث...");

                // Add delay for better UX
                await Task.Delay(500);

                var invoices = await _invoiceService.GetAllInvoicesAsync();
                var filteredInvoices = invoices.AsQueryable();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(InvoiceNumberTextBox.Text))
                {
                    filteredInvoices = filteredInvoices.Where(i =>
                        i.InvoiceNumber.Contains(InvoiceNumberTextBox.Text, StringComparison.OrdinalIgnoreCase));
                }

                if (SupplierComboBox.SelectedValue != null)
                {
                    var selectedSupplierId = (int)SupplierComboBox.SelectedValue;
                    filteredInvoices = filteredInvoices.Where(i => i.SupplierId == selectedSupplierId);
                }

                if (StatusComboBox.SelectedIndex > 0) // Skip "جميع الحالات"
                {
                    var selectedStatusText = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                    var selectedStatus = selectedStatusText switch
                    {
                        "غير مدفوعة" => InvoiceStatus.Unpaid,
                        "مدفوعة جزئياً" => InvoiceStatus.PartiallyPaid,
                        "مدفوعة" => InvoiceStatus.Paid,
                        _ => InvoiceStatus.Unpaid
                    };
                    filteredInvoices = filteredInvoices.Where(i => i.Status == selectedStatus);
                }

                if (FromDatePicker.SelectedDate.HasValue)
                {
                    filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate >= FromDatePicker.SelectedDate.Value);
                }

                if (ToDatePicker.SelectedDate.HasValue)
                {
                    filteredInvoices = filteredInvoices.Where(i => i.InvoiceDate <= ToDatePicker.SelectedDate.Value);
                }

                if (!string.IsNullOrWhiteSpace(AmountTextBox.Text) && decimal.TryParse(AmountTextBox.Text, out decimal amount))
                {
                    filteredInvoices = filteredInvoices.Where(i => i.Amount == amount);
                }

                var results = filteredInvoices.OrderByDescending(i => i.InvoiceDate).ToList();

                Dispatcher.Invoke(() =>
                {
                    SearchResults.Clear();
                    foreach (var invoice in results)
                    {
                        SearchResults.Add(invoice);
                    }

                    UpdateStatistics();
                    UpdateResultsDisplay();
                    _toastService.ShowSuccess("تم البحث بنجاح", $"تم العثور على {results.Count} نتيجة");
                });
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() =>
                {
                    _toastService.ShowError("خطأ في البحث", ex.Message);
                    UpdateResultsInfo("حدث خطأ أثناء البحث");
                });
            }
            finally
            {
                Dispatcher.Invoke(() => ShowLoading(false));
            }
        }

        private void ShowLoading(bool isLoading)
        {
            LoadingPanel.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = isLoading ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateStatistics()
        {
            TotalResults = SearchResults.Count;
            TotalAmount = SearchResults.Sum(i => i.Amount);
            PaidAmount = SearchResults.Sum(i => i.PaidAmount);
            OutstandingAmount = TotalAmount - PaidAmount;

            // Update UI
            TotalResultsText.Text = TotalResults.ToString("N0");
            TotalAmountText.Text = TotalAmount.ToString("N0");
            PaidAmountText.Text = PaidAmount.ToString("N0");
            OutstandingAmountText.Text = OutstandingAmount.ToString("N0");
        }

        private void UpdateResultsDisplay()
        {
            var count = SearchResults.Count;
            ResultsCountText.Text = $"{count} نتيجة";

            EmptyStatePanel.Visibility = count == 0 ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = count == 0 ? Visibility.Collapsed : Visibility.Visible;

            if (count == 0)
            {
                UpdateResultsInfo("لا توجد نتائج مطابقة لمعايير البحث");
            }
            else
            {
                UpdateResultsInfo($"تم العثور على {count} نتيجة مطابقة");
            }
        }

        private void UpdateResultsInfo(string message)
        {
            ResultsInfoText.Text = message;
        }

        private void UpdateEmptyState()
        {
            EmptyStatePanel.Visibility = SearchResults.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
            ResultsDataGrid.Visibility = SearchResults.Count == 0 ? Visibility.Collapsed : Visibility.Visible;
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear all search fields
            InvoiceNumberTextBox.Clear();
            SupplierComboBox.SelectedIndex = -1;
            StatusComboBox.SelectedIndex = 0;
            FromDatePicker.SelectedDate = null;
            ToDatePicker.SelectedDate = null;
            AmountTextBox.Clear();

            // Clear results
            SearchResults.Clear();

            // Reset statistics
            TotalResults = 0;
            TotalAmount = 0;
            PaidAmount = 0;
            OutstandingAmount = 0;

            UpdateStatistics();
            UpdateResultsDisplay();
            UpdateEmptyState();

            _toastService.ShowInfo("تم مسح البحث", "تم مسح جميع معايير البحث والنتائج");
        }

        // Text change handlers for debounced search
        private void InvoiceNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        private void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void FromDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private void ToDatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        private async void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            if (SearchResults.Count == 0)
            {
                _toastService.ShowWarning("لا توجد بيانات للتصدير", "قم بالبحث أولاً للحصول على نتائج");
                return;
            }

            try
            {
                await ExportHelper.ExportInvoicesToExcelAsync(SearchResults.ToList(), "نتائج البحث");
                _toastService.ShowSuccess("تم التصدير بنجاح", $"تم تصدير {SearchResults.Count} فاتورة إلى Excel");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        private async void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            if (SearchResults.Count == 0)
            {
                _toastService.ShowWarning("لا توجد بيانات للتصدير", "قم بالبحث أولاً للحصول على نتائج");
                return;
            }

            try
            {
                await ExportHelper.ExportInvoicesToPdfAsync(SearchResults.ToList(), "نتائج البحث");
                _toastService.ShowSuccess("تم التصدير بنجاح", $"تم تصدير {SearchResults.Count} فاتورة إلى PDF");
            }
            catch (Exception ex)
            {
                _toastService.ShowError("خطأ في التصدير", ex.Message);
            }
        }

        #region New Event Handlers

        // Quick Search
        private void QuickSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _quickSearchTimer.Stop();
            _quickSearchTimer.Start();
            UpdateFilterStatus();
        }

        // Advanced Search Toggle - simplified
        private void AdvancedToggleButton_Click(object sender, RoutedEventArgs e)
        {
            _isAdvancedSearchVisible = !_isAdvancedSearchVisible;
            FiltersExpander.IsExpanded = _isAdvancedSearchVisible;
        }

        // Voice Search (Placeholder)
        private void VoiceSearchButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("البحث الصوتي", "ميزة البحث الصوتي قيد التطوير...");
        }

        // Save Search
        private void SaveSearchButton_Click(object sender, RoutedEventArgs e)
        {
            var searchName = Microsoft.VisualBasic.Interaction.InputBox(
                "أدخل اسم البحث:", "حفظ البحث", $"بحث {DateTime.Now:yyyy-MM-dd HH:mm}");

            if (!string.IsNullOrEmpty(searchName))
            {
                var searchCriteria = new Dictionary<string, object>
                {
                    ["InvoiceNumber"] = InvoiceNumberTextBox.Text,
                    ["SupplierId"] = SupplierComboBox.SelectedValue ?? 0,
                    ["Status"] = StatusComboBox.SelectedIndex,
                    ["FromDate"] = FromDatePicker.SelectedDate ?? DateTime.MinValue,
                    ["ToDate"] = ToDatePicker.SelectedDate ?? DateTime.MaxValue,
                    ["Amount"] = AmountTextBox.Text,
                    ["QuickSearch"] = QuickSearchTextBox.Text
                };

                _savedSearches[searchName] = searchCriteria;
                _toastService.ShowSuccess("تم حفظ البحث", $"تم حفظ البحث باسم: {searchName}");
            }
        }

        // Load Search
        private void LoadSearchButton_Click(object sender, RoutedEventArgs e)
        {
            if (_savedSearches.Count == 0)
            {
                _toastService.ShowInfo("لا توجد بحثات محفوظة", "لم يتم حفظ أي بحثات بعد");
                return;
            }

            // Show saved searches (simplified - you can create a proper dialog)
            var searchNames = string.Join("\n", _savedSearches.Keys);
            _toastService.ShowInfo("البحثات المحفوظة", $"البحثات المتاحة:\n{searchNames}");
        }

        // Clear Search
        private void ClearSearchButton_Click(object sender, RoutedEventArgs e)
        {
            ClearButton_Click(sender, e);
            QuickSearchTextBox.Clear();
        }

        // Search Options Changed
        private void SearchOptions_Changed(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
                UpdateFilterStatus();
            }
        }

        // Date Picker Changed
        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
                UpdateFilterStatus();
            }
        }

        // Sort By Changed
        private void SortByComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            }
        }

        // Results Limit Changed - removed (simplified)

        // Stats Card Hover Effects
        private void StatsCard_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // Add hover effect if needed
        }

        private void StatsCard_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            // Remove hover effect if needed
        }

        // DataGrid Events
        private void ResultsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ResultsDataGrid.SelectedItem is Invoice selectedInvoice)
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), selectedInvoice.Id);
            }
        }

        private void ResultsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Update selection info if needed
        }

        // Action Buttons
        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _navigationService.NavigateTo(typeof(InvoicesPage), invoice.Id);
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _toastService.ShowInfo("تعديل الفاتورة", $"سيتم فتح نافذة تعديل الفاتورة رقم {invoice.InvoiceNumber}");
                // TODO: Open edit window
            }
        }

        private void AddPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _toastService.ShowInfo("إضافة دفعة", $"سيتم فتح نافذة إضافة دفعة للفاتورة رقم {invoice.InvoiceNumber}");
                // TODO: Open payment window
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Invoice invoice)
            {
                _toastService.ShowInfo("طباعة الفاتورة", $"سيتم طباعة الفاتورة رقم {invoice.InvoiceNumber}");
                // TODO: Print invoice
            }
        }

        // Footer Actions
        private void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                UpdatePagination();
                // TODO: Reload data for current page
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage++;
            UpdatePagination();
            // TODO: Reload data for current page
        }

        private void UpdatePagination()
        {
            CurrentPageText.Text = _currentPage.ToString();
            // TODO: Calculate total pages based on results
            TotalPagesText.Text = "1"; // Placeholder
        }

        // New Search Button
        private void NewSearchButton_Click(object sender, RoutedEventArgs e)
        {
            ClearSearchButton_Click(sender, e);
            QuickSearchTextBox.Focus();
        }

        // Enhanced Export Actions
        private void ExportResultsButton_Click(object sender, RoutedEventArgs e)
        {
            ExportExcelButton_Click(sender, e);
        }

        private void PrintResultsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("طباعة النتائج", "سيتم طباعة نتائج البحث...");
            // TODO: Implement print functionality
        }

        private void ShareResultsButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("مشاركة النتائج", "ميزة مشاركة النتائج قيد التطوير...");
            // TODO: Implement share functionality
        }

        // View Mode Toggle
        private void ViewModeButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("تغيير العرض", "أنماط عرض إضافية قيد التطوير...");
            // TODO: Implement different view modes
        }

        // Refresh Results
        private void RefreshResultsButton_Click(object sender, RoutedEventArgs e)
        {
            _allInvoices = new List<Invoice>(); // Force reload
            _searchTimer.Stop();
            _searchTimer.Start();
        }

        // Full Screen
        private void FullScreenButton_Click(object sender, RoutedEventArgs e)
        {
            _toastService.ShowInfo("ملء الشاشة", "ميزة ملء الشاشة قيد التطوير...");
            // TODO: Implement full screen mode
        }

        #endregion

        // Updated existing handlers
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _searchTimer.Stop();
            _searchTimer.Start();
            UpdateFilterStatus();
        }
    }
}
