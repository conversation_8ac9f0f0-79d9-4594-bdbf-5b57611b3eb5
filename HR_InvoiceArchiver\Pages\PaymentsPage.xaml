<UserControl x:Class="HR_InvoiceArchiver.Pages.PaymentsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:HR_InvoiceArchiver.Converters"
             FlowDirection="RightToLeft"
             Background="Transparent">

    <UserControl.Resources>
        <!-- Converters -->
        <local:StringNullOrEmptyConverter x:Key="StringNullOrEmptyConverter"/>

        <!-- حذف جميع الأنماط المحلية للأزرار والبطاقات هنا -->

        <!-- Consistent Color Palette with MainWindow -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#2C3E50"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#007BFF"/>
        <SolidColorBrush x:Key="AccentColor" Color="#28A745"/>
        <SolidColorBrush x:Key="BackgroundColor" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="HeaderColor" Color="#FFFFFF"/>

        <!-- Card Gradients -->
        <LinearGradientBrush x:Key="HeaderGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#007BFF" Offset="0"/>
            <GradientStop Color="#0056B3" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient1" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#E3F2FD" Offset="0"/>
            <GradientStop Color="#BBDEFB" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient2" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#E8F5E8" Offset="0"/>
            <GradientStop Color="#C8E6C9" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient3" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FFF3E0" Offset="0"/>
            <GradientStop Color="#FFE0B2" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="CardGradient4" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#F3E5F5" Offset="0"/>
            <GradientStop Color="#E1BEE7" Offset="1"/>
        </LinearGradientBrush>

        <!-- Modern Card Style -->
        <Style x:Key="ModernCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="ModernActionButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="Margin" Value="8,0"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="{Binding Background.Color, RelativeSource={RelativeSource TemplatedParent}}"
                                                Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Enhanced Modern Header Section -->
        <Border Grid.Row="0" CornerRadius="25" Margin="0,0,0,30">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667EEA" Offset="0"/>
                    <GradientStop Color="#764BA2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="#64748B" Opacity="0.15" BlurRadius="35" ShadowDepth="12"/>
            </Border.Effect>

            <Grid Margin="40,35">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Left Section - Enhanced Icon and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Border Background="#26FFFFFF" CornerRadius="20"
                            Width="80" Height="80" Margin="0,0,25,0">
                        <Border.Effect>
                            <DropShadowEffect Color="#000000" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
                        </Border.Effect>
                        <materialDesign:PackIcon Kind="CreditCardMultiple" Width="45" Height="45"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="إدارة المدفوعات" FontSize="32" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="نظام شامل ومتطور لإدارة المدفوعات والإيصالات" FontSize="16"
                                 Foreground="#D9FFFFFF" Margin="0,8,0,0"/>
                    </StackPanel>
                </StackPanel>

                <!-- Right Section - Action Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- Add Payment Button -->
                    <Border Background="#10B981" CornerRadius="15" Margin="8,0">
                        <Border.Effect>
                            <DropShadowEffect Color="#10B981" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                        </Border.Effect>
                        <Button x:Name="AddPaymentButton" Click="AddPaymentButton_Click"
                               Background="Transparent" Foreground="White" BorderThickness="0"
                               MinWidth="140" Height="45" FontSize="14" FontWeight="SemiBold">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إضافة مدفوعة" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Multi Payment Button -->
                    <Border Background="#3B82F6" CornerRadius="15" Margin="8,0">
                        <Border.Effect>
                            <DropShadowEffect Color="#3B82F6" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                        </Border.Effect>
                        <Button x:Name="AddMultiPaymentButton" Click="AddMultiPaymentButton_Click"
                               Background="Transparent" Foreground="White" BorderThickness="0"
                               MinWidth="120" Height="45" FontSize="14" FontWeight="SemiBold">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Receipt" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="وصل متعدد" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Refresh Button -->
                    <Border Background="#8B5CF6" CornerRadius="15" Margin="8,0">
                        <Border.Effect>
                            <DropShadowEffect Color="#8B5CF6" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                        </Border.Effect>
                        <Button x:Name="RefreshButton" Click="RefreshButton_Click"
                               Background="Transparent" Foreground="White" BorderThickness="0"
                               MinWidth="100" Height="45" FontSize="14" FontWeight="SemiBold">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="تحديث" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Border>

                    <!-- Export Button -->
                    <Border Background="#F59E0B" CornerRadius="15" Margin="8,0">
                        <Border.Effect>
                            <DropShadowEffect Color="#F59E0B" Opacity="0.3" BlurRadius="12" ShadowDepth="3"/>
                        </Border.Effect>
                        <Button x:Name="ExportButton" Click="ExportButton_Click"
                               Background="Transparent" Foreground="White" BorderThickness="0"
                               MinWidth="100" Height="45" FontSize="14" FontWeight="SemiBold">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExport" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="تصدير" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Enhanced Modern Statistics Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Payments Card -->
            <Border Grid.Column="0" Background="White" CornerRadius="18" Padding="20" Margin="8">
                <Border.Effect>
                    <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header with Icon -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <Border Background="#E3F2FD" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="Receipt" Width="22" Height="22"
                                                   Foreground="#1976D2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إجمالي المدفوعات" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                            <TextBlock Text="Total Payments" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Main Value -->
                    <TextBlock Grid.Row="1" x:Name="TotalPaymentsText" Text="0" FontSize="32" FontWeight="Bold"
                             Foreground="#1976D2" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <!-- Footer Badge -->
                    <Border Grid.Row="2" Background="#F0F9FF" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                        <TextBlock Text="دفعة مسجلة" FontSize="12" Foreground="#1976D2" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </Border>

            <!-- Total Amount Card -->
            <Border Grid.Column="1" Background="White" CornerRadius="18" Padding="20" Margin="8">
                <Border.Effect>
                    <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header with Icon -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <Border Background="#E8F5E8" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="22" Height="22"
                                                   Foreground="#388E3C" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="إجمالي المبلغ" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                            <TextBlock Text="Total Amount" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Main Value -->
                    <TextBlock Grid.Row="1" x:Name="TotalAmountText" Text="0" FontSize="32" FontWeight="Bold"
                             Foreground="#388E3C" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <!-- Footer Badge -->
                    <Border Grid.Row="2" Background="#F0FDF4" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                        <TextBlock Text="دينار عراقي" FontSize="12" Foreground="#388E3C" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </Border>

            <!-- Cash Payments Card -->
            <Border Grid.Column="2" Background="White" CornerRadius="18" Padding="20" Margin="8">
                <Border.Effect>
                    <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header with Icon -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <Border Background="#FFF3E0" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="Cash" Width="22" Height="22"
                                                   Foreground="#F57C00" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="مدفوعات نقدية" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                            <TextBlock Text="Cash Payments" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Main Value -->
                    <TextBlock Grid.Row="1" x:Name="CashPaymentsText" Text="0" FontSize="32" FontWeight="Bold"
                             Foreground="#F57C00" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <!-- Footer Badge -->
                    <Border Grid.Row="2" Background="#FFFBF0" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                        <TextBlock Text="دفعة نقدية" FontSize="12" Foreground="#F57C00" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </Border>

            <!-- Card Payments Card -->
            <Border Grid.Column="3" Background="White" CornerRadius="18" Padding="20" Margin="8">
                <Border.Effect>
                    <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                </Border.Effect>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header with Icon -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                        <Border Background="#F3E5F5" CornerRadius="12" Width="40" Height="40" Margin="0,0,12,0">
                            <materialDesign:PackIcon Kind="CreditCardOutline" Width="22" Height="22"
                                                   Foreground="#7B1FA2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="مدفوعات بطاقة" FontSize="14" Foreground="#1E293B" FontWeight="Bold"/>
                            <TextBlock Text="Card Payments" FontSize="11" Foreground="#64748B" Margin="0,1,0,0"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Main Value -->
                    <TextBlock Grid.Row="1" x:Name="CardPaymentsText" Text="0" FontSize="32" FontWeight="Bold"
                             Foreground="#7B1FA2" HorizontalAlignment="Center" VerticalAlignment="Center"/>

                    <!-- Footer Badge -->
                    <Border Grid.Row="2" Background="#FAF5FF" CornerRadius="10" Padding="12,6" HorizontalAlignment="Center">
                        <TextBlock Text="دفعة بطاقة" FontSize="12" Foreground="#7B1FA2" FontWeight="SemiBold"/>
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- Enhanced Modern Main Content -->
        <Border Grid.Row="2" Background="White" CornerRadius="25" Padding="30" Margin="0">
            <Border.Effect>
                <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="30" ShadowDepth="8"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Enhanced Modern Search Section -->
                <Border Grid.Row="0" Background="#F8FAFC" CornerRadius="15" Padding="18" Margin="0,0,0,15">
                    <Border.Effect>
                        <DropShadowEffect Color="#64748B" Opacity="0.05" BlurRadius="15" ShadowDepth="3"/>
                    </Border.Effect>
                    <StackPanel>
                        <!-- Main Search Row -->
                        <Grid Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Enhanced Search Box -->
                            <Border Grid.Column="0" Background="White" CornerRadius="15" Padding="18,12" Margin="0,0,20,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#64748B" Opacity="0.08" BlurRadius="12" ShadowDepth="2"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="20" Height="20"
                                                           Foreground="#64748B" Margin="0,0,12,0" VerticalAlignment="Center"/>
                                    <TextBox Grid.Column="1" x:Name="SearchTextBox" BorderThickness="0" Background="Transparent"
                                            materialDesign:HintAssist.Hint="البحث في رقم الإيصال، المورد، أو التفاصيل..."
                                            FontSize="15" VerticalAlignment="Center"
                                            TextChanged="SearchTextBox_TextChanged"/>
                                </Grid>
                            </Border>

                            <!-- Action Buttons -->
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Border Background="#EF4444" CornerRadius="12" Margin="6,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#EF4444" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                    </Border.Effect>
                                    <Button Background="Transparent" Foreground="White" BorderThickness="0"
                                           MinWidth="100" Height="38" FontSize="13" FontWeight="SemiBold"
                                           Click="ClearSearchButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Margin="0,0,6,0"/>
                                            <TextBlock Text="مسح البحث" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </Border>

                                <Border Background="#6366F1" CornerRadius="12" Margin="6,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#6366F1" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                    </Border.Effect>
                                    <Button x:Name="ToggleFiltersButton" Background="Transparent" Foreground="White" BorderThickness="0"
                                           MinWidth="120" Height="38" FontSize="13" FontWeight="SemiBold"
                                           Click="ToggleFilters_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Filter" Width="16" Height="16" Margin="0,0,6,0"/>
                                            <TextBlock Text="تصفية متقدمة" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </Border>

                                <!-- Payment Status Quick Filter Buttons -->
                                <Button Background="#10B981" Foreground="White" BorderThickness="0"
                                       MinWidth="100" Height="36" Margin="4,0" ToolTip="عرض التسديدات الكاملة فقط"
                                       Click="QuickFilterFullPayment_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="15">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#059669"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CheckCircle" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="تسديد كامل" FontSize="11" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <Button Background="#F59E0B" Foreground="White" BorderThickness="0"
                                       MinWidth="100" Height="36" Margin="4,0" ToolTip="عرض التسديدات الجزئية فقط"
                                       Click="QuickFilterPartialPayment_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="15">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#D97706"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ClockOutline" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="تسديد جزئي" FontSize="11" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <Button Background="#8B5CF6" Foreground="White" BorderThickness="0"
                                       MinWidth="110" Height="36" Margin="4,0" ToolTip="عرض التسديدات مع خصم فقط"
                                       Click="QuickFilterWithDiscount_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="15">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#7C3AED"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Tag" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="تسديد وبخصم" FontSize="11" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>

                                <Button Background="#EF4444" Foreground="White" BorderThickness="0"
                                       MinWidth="120" Height="36" Margin="4,0" ToolTip="عرض التسديدات مع استرجاع فقط"
                                       Click="QuickFilterWithRefund_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border x:Name="border" Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="15">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="border" Property="Background" Value="#DC2626"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ArrowLeftBold" Width="14" Height="14" Margin="0,0,4,0"/>
                                        <TextBlock Text="تسديد واسترجاع" FontSize="10" FontWeight="SemiBold"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- Enhanced Advanced Filters Panel -->
                        <Border x:Name="FiltersPanel" Background="White" CornerRadius="15" Padding="20"
                               Visibility="Collapsed" Margin="0,10,0,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#64748B" Opacity="0.1" BlurRadius="20" ShadowDepth="4"/>
                            </Border.Effect>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Enhanced Filter Title -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,25">
                                    <Border Background="#EEF2FF" CornerRadius="12" Padding="12" Margin="0,0,15,0">
                                        <materialDesign:PackIcon Kind="FilterVariant" Width="24" Height="24" Foreground="#6366F1"/>
                                    </Border>
                                    <StackPanel VerticalAlignment="Center">
                                        <TextBlock Text="تصفية متقدمة" FontWeight="Bold" FontSize="18" Foreground="#1E293B"/>
                                        <TextBlock Text="استخدم الفلاتر لتضييق نطاق البحث" FontSize="13" Foreground="#64748B" Margin="0,2,0,0"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Enhanced Filter Controls -->
                                <Grid Grid.Row="1" Margin="0,0,0,25">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Enhanced Date Range -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,20,20">
                                        <TextBlock Text="من تاريخ:" FontWeight="SemiBold" FontSize="14" Foreground="#374151" Margin="0,0,0,8"/>
                                        <Border Background="#F9FAFB" CornerRadius="12" Padding="15,10">
                                            <DatePicker x:Name="FromDatePicker" BorderThickness="0" Background="Transparent"
                                                      materialDesign:HintAssist.Hint="اختر التاريخ" FontSize="14"
                                                      SelectedDateChanged="DateFilter_Changed"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,20,20">
                                        <TextBlock Text="إلى تاريخ:" FontWeight="SemiBold" FontSize="14" Foreground="#374151" Margin="0,0,0,8"/>
                                        <Border Background="#F9FAFB" CornerRadius="12" Padding="15,10">
                                            <DatePicker x:Name="ToDatePicker" BorderThickness="0" Background="Transparent"
                                                      materialDesign:HintAssist.Hint="اختر التاريخ" FontSize="14"
                                                      SelectedDateChanged="DateFilter_Changed"/>
                                        </Border>
                                    </StackPanel>

                                    <!-- Enhanced Amount Range -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,20,20">
                                        <TextBlock Text="المبلغ من:" FontWeight="SemiBold" FontSize="14" Foreground="#374151" Margin="0,0,0,8"/>
                                        <Border Background="#F9FAFB" CornerRadius="12" Padding="15,10">
                                            <TextBox x:Name="MinAmountTextBox" BorderThickness="0" Background="Transparent"
                                                   materialDesign:HintAssist.Hint="أقل مبلغ" FontSize="14"
                                                   TextChanged="AmountFilter_Changed"/>
                                        </Border>
                                    </StackPanel>

                                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="0,0,0,20">
                                        <TextBlock Text="المبلغ إلى:" FontWeight="SemiBold" FontSize="14" Foreground="#374151" Margin="0,0,0,8"/>
                                        <Border Background="#F9FAFB" CornerRadius="12" Padding="15,10">
                                            <TextBox x:Name="MaxAmountTextBox" BorderThickness="0" Background="Transparent"
                                                   materialDesign:HintAssist.Hint="أعلى مبلغ" FontSize="14"
                                                   TextChanged="AmountFilter_Changed"/>
                                        </Border>
                                    </StackPanel>

                                    <!-- Payment Method and Status -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,15,0">
                                        <TextBlock Text="طريقة الدفع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <ComboBox x:Name="PaymentMethodComboBox"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                materialDesign:HintAssist.Hint="اختر طريقة الدفع"
                                                SelectionChanged="PaymentMethodFilter_Changed">
                                            <ComboBoxItem Content="جميع الطرق" Tag="All"/>
                                            <ComboBoxItem Content="نقدي" Tag="Cash"/>
                                            <ComboBoxItem Content="شيك" Tag="Check"/>
                                            <ComboBoxItem Content="تحويل بنكي" Tag="BankTransfer"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="0,0,15,0">
                                        <TextBlock Text="حالة الدفع:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                        <ComboBox x:Name="PaymentStatusComboBox"
                                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                                materialDesign:HintAssist.Hint="اختر حالة الدفع"
                                                SelectionChanged="PaymentStatusFilter_Changed">
                                            <ComboBoxItem Content="جميع الحالات" Tag="All"/>
                                            <ComboBoxItem Content="دفعة جزئية" Tag="PartialPayment"/>
                                            <ComboBoxItem Content="دفعة كاملة" Tag="FullPayment"/>
                                            <ComboBoxItem Content="دفعة زائدة" Tag="OverPayment"/>
                                        </ComboBox>
                                    </StackPanel>
                                </Grid>

                                <!-- Filter Actions -->
                                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button x:Name="ApplyFiltersButton"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Background="{StaticResource AccentColor}"
                                          Foreground="White"
                                          Width="120"
                                          Height="35"
                                          Margin="0,0,10,0"
                                          Click="ApplyFilters_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FilterCheck" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="تطبيق" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>

                                    <Button x:Name="ClearFiltersButton"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          BorderBrush="#6C757D"
                                          Foreground="#6C757D"
                                          Width="120"
                                          Height="35"
                                          Click="ClearFilters_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,5,0"/>
                                            <TextBlock Text="مسح الكل" FontWeight="SemiBold"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Enhanced Payments DataGrid -->
                <Border Grid.Row="1" Background="White" CornerRadius="18" Padding="5" Margin="0">
                    <Border.Effect>
                        <DropShadowEffect Color="#64748B" Opacity="0.08" BlurRadius="20" ShadowDepth="4"/>
                    </Border.Effect>
                    <DataGrid x:Name="PaymentsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Background="Transparent"
                             AlternatingRowBackground="#FAFBFC"
                             RowHeight="60"
                             FontSize="14"
                             materialDesign:DataGridAssist.CellPadding="15,12"
                             materialDesign:DataGridAssist.ColumnHeaderPadding="15,15"
                             BorderThickness="0"
                             CanUserSortColumns="True"
                             CanUserReorderColumns="True"
                             CanUserResizeColumns="True"
                             EnableRowVirtualization="True"
                             EnableColumnVirtualization="True"
                             MinHeight="600">

                        <DataGrid.Resources>
                            <Style TargetType="DataGridColumnHeader">
                                <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Height" Value="50"/>
                                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                <Setter Property="VerticalContentAlignment" Value="Center"/>
                            </Style>

                            <Style TargetType="DataGridRow">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E3F2FD"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="#BBDEFB"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="رقم الإيصال" Binding="{Binding ReceiptNumber}" Width="150"
                                              SortDirection="Descending" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المورد" Binding="{Binding SupplierName}" Width="220" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                        <Setter Property="ToolTip" Value="{Binding SupplierName}"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="130" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Medium"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ (د.ع)" Binding="{Binding Amount, StringFormat=N0}" Width="130" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="{StaticResource AccentColor}"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontSize" Value="14"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الخصم" Binding="{Binding DiscountAmount, StringFormat=N0}" Width="90" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#FF6B35"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الاسترجاع" Binding="{Binding RefundValue, StringFormat=N0}" Width="90" CanUserSort="True">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="Foreground" Value="#9C27B0"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="حالة التسديد" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                            <Setter Property="Background" Value="#E8F5E8"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding StatusText}" FontSize="11" FontWeight="SemiBold" HorizontalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="FullPayment">
                                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PartialPayment">
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PaymentWithDiscount">
                                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="PaymentWithRefund">
                                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="المتبقي" Binding="{Binding Invoice.RemainingAmount, StringFormat=N0}" Width="120">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#D32F2F"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="طريقة الدفع" Width="140">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                            <Border.Style>
                                                <Style TargetType="Border">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                            <Setter Property="Background" Value="#FFF3E0"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                            <Setter Property="Background" Value="#F3E5F5"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </Border.Style>
                                            <TextBlock Text="{Binding PaymentMethodText}" FontSize="11" FontWeight="SemiBold">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="نقدي">
                                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding PaymentMethodText}" Value="بطاقة ائتمان">
                                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Border>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#6C757D"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="Margin" Value="10,0"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="220" CanUserSort="False" CanUserReorder="False">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                            <!-- View Receipt Attachment Button -->
                                            <Border Background="#FFF3E0" CornerRadius="8" Margin="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#FF6B35" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                                </Border.Effect>
                                                <Button ToolTip="عرض مستند الوصل"
                                                       Width="32" Height="32"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Tag="{Binding}"
                                                       Click="ViewReceiptAttachment_Click">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding AttachmentPath, Converter={StaticResource StringNullOrEmptyConverter}}" Value="True">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                </DataTrigger>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#33FF6B35"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                    <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"
                                                                           Foreground="#FF6B35"/>
                                                </Button>
                                            </Border>

                                            <!-- View Details Button -->
                                            <Border Background="#E3F2FD" CornerRadius="8" Margin="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#2196F3" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                                </Border.Effect>
                                                <Button ToolTip="عرض التفاصيل"
                                                       Width="32" Height="32"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Tag="{Binding}"
                                                       Click="ViewPaymentDetails_Click">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#332196F3"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                    <materialDesign:PackIcon Kind="InformationOutline" Width="16" Height="16"
                                                                           Foreground="#2196F3"/>
                                                </Button>
                                            </Border>



                                            <!-- Edit Button -->
                                            <Border Background="#E8F5E8" CornerRadius="8" Margin="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#4CAF50" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                                </Border.Effect>
                                                <Button ToolTip="تعديل"
                                                       Width="32" Height="32"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Tag="{Binding}"
                                                       Click="EditPayment_Click">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#334CAF50"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                    <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"
                                                                           Foreground="#4CAF50"/>
                                                </Button>
                                            </Border>

                                            <!-- Delete Button -->
                                            <Border Background="#FFEBEE" CornerRadius="8" Margin="2">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#F44336" Opacity="0.15" BlurRadius="6" ShadowDepth="2"/>
                                                </Border.Effect>
                                                <Button ToolTip="حذف"
                                                       Width="32" Height="32"
                                                       Background="Transparent"
                                                       BorderThickness="0"
                                                       Tag="{Binding}"
                                                       Click="DeletePayment_Click">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#33F44336"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"
                                                                           Foreground="#F44336"/>
                                                </Button>
                                            </Border>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
            </Grid>
        </Border>

        <!-- Enhanced Loading Panel -->
        <Grid x:Name="LoadingPanel"
              Grid.Row="2"
              Visibility="Collapsed"
              Background="#F2F8F9FA">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="50" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <Border Background="{StaticResource SecondaryColor}" CornerRadius="50"
                            Width="80" Height="80" Margin="0,0,0,25">
                        <materialDesign:PackIcon Kind="Loading" Width="40" Height="40"
                                               Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center">
                            <materialDesign:PackIcon.RenderTransform>
                                <RotateTransform/>
                            </materialDesign:PackIcon.RenderTransform>
                            <materialDesign:PackIcon.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                           From="0" To="360" Duration="0:0:2"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </materialDesign:PackIcon.Triggers>
                        </materialDesign:PackIcon>
                    </Border>
                    <TextBlock Text="جاري تحميل المدفوعات..." FontSize="20" FontWeight="SemiBold"
                             Foreground="{StaticResource SecondaryColor}" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="يرجى الانتظار..." FontSize="14" Foreground="#6C757D" HorizontalAlignment="Center"/>
                    <ProgressBar IsIndeterminate="True" Width="250" Height="6" Margin="0,20,0,0"
                               Foreground="{StaticResource SecondaryColor}"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Enhanced Empty State Panel -->
        <Grid x:Name="EmptyStatePanel"
              Grid.Row="2"
              Visibility="Collapsed"
              Background="#F2F8F9FA">
            <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                               Padding="60" Style="{StaticResource ModernCardStyle}">
                <StackPanel HorizontalAlignment="Center">
                    <Border Background="#F8F9FA" CornerRadius="50" Width="100" Height="100" Margin="0,0,0,25">
                        <materialDesign:PackIcon Kind="CreditCardOff" Width="50" Height="50"
                                               Foreground="#6C757D" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock Text="لا توجد مدفوعات" FontSize="24" FontWeight="Bold"
                             Foreground="{StaticResource PrimaryColor}" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="لم يتم العثور على أي مدفوعات مطابقة للبحث" FontSize="16"
                             Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,25"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Style="{StaticResource ModernActionButtonStyle}"
                               Background="{StaticResource AccentColor}"
                               Margin="10,0"
                               Click="AddPaymentButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="إضافة أول مدفوعة"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource ModernActionButtonStyle}"
                               Background="#17A2B8"
                               Margin="10,0"
                               Click="ClearSearchButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FilterRemove" Width="18" Height="18" Margin="0,0,8,0"/>
                                <TextBlock Text="مسح الفلاتر"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Multi-Payment Overlay -->
        <Grid x:Name="MultiPaymentOverlay"
              Grid.RowSpan="3"
              Visibility="Collapsed"
              Background="#80000000">
            <Border Background="Transparent"
                    CornerRadius="12"
                    MaxWidth="1600"
                    MaxHeight="1000"
                    MinWidth="1400"
                    MinHeight="900"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                </Border.Effect>

                <Grid x:Name="MultiPaymentContainer">
                    <!-- MultiPaymentFormControl will be added here dynamically -->
                </Grid>
            </Border>
        </Grid>

        <!-- Payment Details Overlay -->
        <Grid x:Name="PaymentDetailsOverlay"
              Grid.RowSpan="3"
              Visibility="Collapsed"
              Background="#80000000">
            <Border Background="White"
                    CornerRadius="12"
                    MaxWidth="800"
                    MaxHeight="900"
                    MinWidth="700"
                    MinHeight="600"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Margin="20">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="20" ShadowDepth="5"/>
                </Border.Effect>

                <Grid x:Name="PaymentDetailsContainer">
                    <!-- PaymentDetailsControl will be added here dynamically -->
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
