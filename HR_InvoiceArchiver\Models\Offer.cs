using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HR_InvoiceArchiver.Models
{
    /// <summary>
    /// نموذج عرض المندوب - نسخة مبسطة
    /// </summary>
    public class Offer
    {
        /// <summary>
        /// المعرف الفريد للعرض
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// اسم المكتب العلمي
        /// </summary>
        [Required(ErrorMessage = "اسم المكتب العلمي مطلوب")]
        [StringLength(100)]
        public string ScientificOffice { get; set; } = string.Empty;

        /// <summary>
        /// اسم المندوب
        /// </summary>
        [Required(ErrorMessage = "اسم المندوب مطلوب")]
        [StringLength(100)]
        public string RepresentativeName { get; set; } = string.Empty;

        /// <summary>
        /// رقم هاتف المندوب
        /// </summary>
        [StringLength(20)]
        public string? RepresentativePhone { get; set; }

        /// <summary>
        /// اسم المادة العلمية
        /// </summary>
        [Required(ErrorMessage = "اسم المادة العلمية مطلوب")]
        [StringLength(100)]
        public string ScientificName { get; set; } = string.Empty;

        /// <summary>
        /// اسم المادة التجارية
        /// </summary>
        [StringLength(100)]
        public string? TradeName { get; set; }

        /// <summary>
        /// السعر
        /// </summary>
        [Required(ErrorMessage = "السعر مطلوب")]
        [Column(TypeName = "decimal(18,2)")]
        [Range(0.01, double.MaxValue, ErrorMessage = "السعر يجب أن يكون أكبر من صفر")]
        public decimal Price { get; set; }

        /// <summary>
        /// البونص أو الخصم
        /// </summary>
        [StringLength(200)]
        public string? BonusOrDiscount { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// مسار المرفق المحلي (مؤقتاً معطل)
        /// </summary>
        [StringLength(500)]
        public string? AttachmentPath { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// حالة النشاط (للحذف المنطقي)
        /// </summary>
        public bool IsActive { get; set; } = true;

        // خصائص محسوبة للعرض
        /// <summary>
        /// نص وصفي للبونص أو الخصم
        /// </summary>
        [NotMapped]
        public string BonusDiscountDisplay => string.IsNullOrEmpty(BonusOrDiscount) ? "لا يوجد" : BonusOrDiscount;

        /// <summary>
        /// عرض مختصر للعرض
        /// </summary>
        [NotMapped]
        public string DisplaySummary => $"{ScientificName} - {Price:C} - {ScientificOffice}";

        /// <summary>
        /// هل يوجد مرفق
        /// </summary>
        [NotMapped]
        public bool HasAttachment => !string.IsNullOrEmpty(AttachmentPath);

        /// <summary>
        /// عرض معلومات المرفق
        /// </summary>
        [NotMapped]
        public string AttachmentInfo => HasAttachment ? "يوجد مرفق" : "لا يوجد مرفق";
    }
}
