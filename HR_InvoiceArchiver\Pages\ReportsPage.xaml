<UserControl x:Class="HR_InvoiceArchiver.Pages.ReportsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             FlowDirection="RightToLeft"
             Background="#F8F9FF">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Modern Header -->
        <Border Background="#667eea" CornerRadius="16" Padding="32,24" Margin="0,0,0,24">
            <Border.Effect>
                <DropShadowEffect Color="#667eea" Opacity="0.3" BlurRadius="20" ShadowDepth="8"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="ChartLine" Width="32" Height="32" Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Text="التقارير والإحصائيات المتقدمة" FontSize="28" FontWeight="Bold"
                                 Foreground="White" VerticalAlignment="Center" Margin="16,0,0,0"/>
                    </StackPanel>
                    <TextBlock Text="تحليل شامل للبيانات المالية مع إحصائيات تفاعلية ومرئية"
                             FontSize="16" Foreground="#E0E7FF" Margin="48,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="SettingsButton" Style="{StaticResource MaterialDesignIconButton}"
                            Width="48" Height="48"
                            ToolTip="إعدادات التقارير" Click="SettingsButton_Click">
                        <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
        

                
        <!-- Report Types Section -->
        <Grid Grid.Row="1" Margin="0,0,0,24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Invoices Report Button -->
            <Border Grid.Column="0" Background="#667eea" CornerRadius="16" Margin="0,0,8,0">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" Opacity="0.25" BlurRadius="15" ShadowDepth="5"/>
                </Border.Effect>
                <Button x:Name="InvoicesReportButton" Background="Transparent" BorderThickness="0"
                        Height="140" Click="InvoicesReportButton_Click" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF20"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF30"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#FFFFFF20" CornerRadius="25" Width="50" Height="50"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="FileDocument" Width="28" Height="28"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="تقرير الفواتير" FontSize="16" FontWeight="SemiBold"
                                 Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                        <TextBlock Text="جميع الفواتير والتفاصيل" FontSize="12"
                                 Foreground="White" HorizontalAlignment="Center" TextWrapping="Wrap" Opacity="0.9"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- Payments Report Button -->
            <Border Grid.Column="1" Background="#10B981" CornerRadius="16" Margin="4,0,4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#10B981" Opacity="0.25" BlurRadius="15" ShadowDepth="5"/>
                </Border.Effect>
                <Button x:Name="PaymentsReportButton" Background="Transparent" BorderThickness="0"
                        Height="140" Click="PaymentsReportButton_Click" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF20"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF30"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#FFFFFF20" CornerRadius="25" Width="50" Height="50"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="28" Height="28"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="تقرير المدفوعات" FontSize="16" FontWeight="SemiBold"
                                 Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                        <TextBlock Text="تتبع المدفوعات والتحصيلات" FontSize="12"
                                 Foreground="White" HorizontalAlignment="Center" TextWrapping="Wrap" Opacity="0.9"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- Suppliers Report Button -->
            <Border Grid.Column="2" Background="#4ECDC4" CornerRadius="16" Margin="4,0,4,0">
                <Border.Effect>
                    <DropShadowEffect Color="#4ECDC4" Opacity="0.25" BlurRadius="15" ShadowDepth="5"/>
                </Border.Effect>
                <Button x:Name="SuppliersReportButton" Background="Transparent" BorderThickness="0"
                        Height="140" Click="SuppliersReportButton_Click" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF20"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF30"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#FFFFFF20" CornerRadius="25" Width="50" Height="50"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="AccountGroup" Width="28" Height="28"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="تقرير الموردين" FontSize="16" FontWeight="SemiBold"
                                 Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                        <TextBlock Text="إحصائيات وأداء الموردين" FontSize="12"
                                 Foreground="White" HorizontalAlignment="Center" TextWrapping="Wrap" Opacity="0.9"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- Monthly Report Button -->
            <Border Grid.Column="3" Background="#8B5CF6" CornerRadius="16" Margin="8,0,0,0">
                <Border.Effect>
                    <DropShadowEffect Color="#8B5CF6" Opacity="0.25" BlurRadius="15" ShadowDepth="5"/>
                </Border.Effect>
                <Button x:Name="MonthlyReportButton" Background="Transparent" BorderThickness="0"
                        Height="140" Click="MonthlyReportButton_Click" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF20"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#FFFFFF30"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#FFFFFF20" CornerRadius="25" Width="50" Height="50"
                                HorizontalAlignment="Center" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="ChartLine" Width="28" Height="28"
                                                   Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <TextBlock Text="التقرير الشهري" FontSize="16" FontWeight="SemiBold"
                                 Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,6"/>
                        <TextBlock Text="تحليل شهري مفصل" FontSize="12"
                                 Foreground="White" HorizontalAlignment="Center" TextWrapping="Wrap" Opacity="0.9"/>
                    </StackPanel>
                </Button>
            </Border>
        </Grid>

        <!-- Report Display Section -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <Border Background="White" CornerRadius="16" Margin="0">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" Opacity="0.15" BlurRadius="20" ShadowDepth="8"/>
                </Border.Effect>

                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Report Header -->
                    <Border Grid.Row="0" Background="#667eea" CornerRadius="16,16,0,0" Padding="24,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileChart" Width="28" Height="28" Foreground="White" VerticalAlignment="Center"/>
                                <StackPanel Margin="16,0,0,0">
                                    <TextBlock x:Name="ReportTitleText" Text="اختر نوع التقرير" FontSize="22" FontWeight="Bold" Foreground="White"/>
                                    <TextBlock x:Name="ReportDateText" Text="استخدم الأزرار أعلاه لعرض التقارير المختلفة"
                                             FontSize="14" Foreground="#E0E7FF" Margin="0,4,0,0"/>
                                </StackPanel>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button x:Name="LoadDataButton" Style="{StaticResource MaterialDesignRaisedButton}"
                                        Background="White" Foreground="#667eea" BorderBrush="White"
                                        Height="40" Padding="16,0" Margin="0,0,12,0"
                                        Click="LoadDataButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="تحميل البيانات" FontSize="14"/>
                                    </StackPanel>
                                </Button>

                                <Button x:Name="ExportButton" Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Foreground="White" BorderBrush="White"
                                        Height="40" Padding="16,0"
                                        Click="ExportButton_Click" IsEnabled="False">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Download" Width="18" Height="18" Margin="0,0,8,0"/>
                                        <TextBlock Text="تصدير" FontSize="14"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Enhanced Search Bar -->
                    <Border Grid.Row="1" Background="#F8F9FF" Padding="24,20">
                        <Border Background="White" CornerRadius="12" Padding="16,12">
                            <Border.Effect>
                                <DropShadowEffect Color="#667eea" Opacity="0.1" BlurRadius="10" ShadowDepth="3"/>
                            </Border.Effect>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <materialDesign:PackIcon Grid.Column="0" Kind="Magnify" Width="20" Height="20"
                                                       Foreground="#667eea" VerticalAlignment="Center" Margin="0,0,12,0"/>

                                <TextBox x:Name="SearchTextBox" Grid.Column="1"
                                       Background="Transparent" BorderThickness="0"
                                       FontSize="14" VerticalAlignment="Center"
                                       materialDesign:HintAssist.Hint="البحث في التقرير..."
                                       materialDesign:HintAssist.Foreground="#999999"
                                       TextChanged="SearchTextBox_TextChanged"/>

                                <Button x:Name="ClearSearchButton" Grid.Column="2"
                                      Style="{StaticResource MaterialDesignIconButton}"
                                      Width="32" Height="32" Margin="8,0,0,0"
                                      Click="ClearSearchButton_Click"
                                      Visibility="Collapsed">
                                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16" Foreground="#999999"/>
                                </Button>
                            </Grid>
                        </Border>
                    </Border>
                    <!-- Data Grid and Content -->
                    <Grid Grid.Row="2" Margin="24">
                        <!-- Enhanced Data Grid -->
                        <Border Background="White" CornerRadius="8">
                            <DataGrid x:Name="ReportDataGrid"
                                      Background="Transparent"
                                      BorderThickness="0"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      AlternatingRowBackground="#FAFBFC"
                                      RowBackground="White"
                                      FontSize="14"
                                      FlowDirection="RightToLeft"
                                      materialDesign:DataGridAssist.CellPadding="16"
                                      materialDesign:DataGridAssist.ColumnHeaderPadding="16">
                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource MaterialDesignDataGridColumnHeader}">
                                        <Setter Property="Background" Value="#667eea"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="Height" Value="52"/>
                                        <Setter Property="BorderBrush" Value="#5a67d8"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>
                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                                        <Setter Property="Height" Value="56"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F0F4FF"/>
                                            </Trigger>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#E0E7FF"/>
                                                <Setter Property="BorderBrush" Value="#667eea"/>
                                                <Setter Property="BorderThickness" Value="2,0,0,0"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>
                            </DataGrid>
                        </Border>

                        <!-- Enhanced Empty State -->
                        <Border x:Name="EmptyStatePanel" Background="#F8F9FF" CornerRadius="12"
                                Padding="60" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="Visible">
                            <Border.Effect>
                                <DropShadowEffect Color="#667eea" Opacity="0.1" BlurRadius="15" ShadowDepth="5"/>
                            </Border.Effect>

                            <StackPanel HorizontalAlignment="Center" MaxWidth="400">
                                <Border Background="#667eea" CornerRadius="40" Width="80" Height="80"
                                        HorizontalAlignment="Center" Margin="0,0,0,24">
                                    <materialDesign:PackIcon Kind="ChartBoxOutline" Width="40" Height="40"
                                                           Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>

                                <TextBlock x:Name="StatusText" Text="اختر نوع التقرير وانقر على 'تحميل البيانات'"
                                         FontSize="20" FontWeight="SemiBold"
                                         Foreground="#2D3748" HorizontalAlignment="Center" Margin="0,0,0,12"/>
                                <TextBlock Text="ستظهر البيانات والإحصائيات هنا بعد التحميل" FontSize="16"
                                         Foreground="#718096" HorizontalAlignment="Center" TextWrapping="Wrap" Margin="0,0,0,24"/>

                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="تحميل البيانات" Style="{StaticResource MaterialDesignRaisedButton}"
                                            Background="#667eea" BorderBrush="#667eea" Height="44" Padding="20,0"
                                            Click="LoadDataButton_Click" Margin="0,0,12,0"/>
                                    <Button Content="مساعدة" Style="{StaticResource MaterialDesignOutlinedButton}"
                                            BorderBrush="#667eea" Foreground="#667eea" Height="44" Padding="20,0"
                                            Click="HelpButton_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Grid>


                </Grid>
            </Border>
        </ScrollViewer>
    </Grid>
</UserControl>
