# أيقونات بديلة - MaterialDesign PackIcon

## في حالة عدم عمل الأيقونات الحالية

### لوحة التحكم
- `ViewDashboard` (الحالي) ✅
- `Monitor` (بديل 1)
- `ViewGrid` (بديل 2)
- `Home` (بديل 3)

### الفواتير
- `FileDocumentOutline` (الحالي) ✅
- `FileDocument` (بديل 1)
- `Receipt` (بديل 2)
- `FileText` (بديل 3)

### المدفوعات
- `CreditCard` (الحالي) ✅
- `CashMultiple` (بديل 1)
- `Wallet` (بديل 2)
- `CurrencyUsd` (بديل 3)

### الموردين
- `TruckDelivery` (الحالي) ✅
- `AccountGroup` (بديل 1)
- `Domain` (بديل 2)
- `Factory` (بديل 3)

### البحث
- `Magnify` (الحالي) ✅
- `Search` (بديل 1)
- `FileSearch` (بديل 2)
- `DatabaseSearch` (بديل 3)

### التقارير
- `ChartLine` (الحالي) ✅
- `ChartBar` (بديل 1)
- `FileChart` (بديل 2)
- `Analytics` (بديل 3)

### الإعدادات
- `Cog` (الحالي) ✅
- `Settings` (بديل 1)
- `Wrench` (بديل 2)
- `Tune` (بديل 3)

### الاستيراد والتصدير
- `Import` (الحالي) ✅
- `Export` (بديل 1)
- `DatabaseImport` (بديل 2)
- `FileImport` (بديل 3)

### النسخ الاحتياطي
- `ContentSave` (الحالي) ✅
- `CloudDownload` (بديل 1)
- `DatabaseExport` (بديل 2)
- `Archive` (بديل 3)

### تحسين الأداء
- `Flash` (الحالي) ✅
- `Speedometer` (بديل 1)
- `RocketLaunch` (بديل 2)
- `TrendingUp` (بديل 3)

### التخزين السحابي
- `CloudUpload` (الحالي) ✅
- `Cloud` (بديل 1)
- `CloudSync` (بديل 2)
- `GoogleDrive` (بديل 3)

### أزرار النافذة
- `Close` (إغلاق) ✅
- `WindowMaximize` (تكبير) ✅
- `WindowMinimize` (تصغير) ✅

## كيفية تغيير الأيقونات

1. افتح ملف `MainWindow.xaml`
2. ابحث عن الزر المطلوب
3. غيّر قيمة `Tag` إلى الأيقونة الجديدة

مثال:
```xml
<!-- قبل التغيير -->
<Button Tag="ViewDashboard" Content="لوحة التحكم"/>

<!-- بعد التغيير -->
<Button Tag="Monitor" Content="لوحة التحكم"/>
```

## اختبار الأيقونات

يمكنك استخدام ملف `IconTest.xaml` لاختبار الأيقونات قبل تطبيقها في الواجهة الرئيسية.

## ملاحظات مهمة

- تأكد من أن اسم الأيقونة صحيح ومتوفر في MaterialDesign
- جميع الأيقونات حساسة لحالة الأحرف (Case Sensitive)
- يمكن العثور على قائمة كاملة بالأيقونات في: https://materialdesignicons.com/
- الأيقونات المستخدمة متوافقة مع MaterialDesignThemes 5.2.1
