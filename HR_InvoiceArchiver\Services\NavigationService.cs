using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Controls;
using System.Windows.Threading;
using Microsoft.Extensions.DependencyInjection;

namespace HR_InvoiceArchiver.Services
{
    public class NavigationService : INavigationService
    {
        private readonly Stack<NavigationItem> _navigationStack = new();
        private readonly IServiceProvider _serviceProvider;

        public NavigationService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public event EventHandler<NavigationEventArgs>? Navigated;

        public UserControl? CurrentPage { get; private set; }
        public string CurrentPageTitle { get; private set; } = string.Empty;
        public bool CanGoBack => _navigationStack.Count > 1;

        public void NavigateTo<T>() where T : UserControl, new()
        {
            NavigateTo(typeof(T), null);
        }

        public void NavigateTo<T>(object? parameter) where T : UserControl, new()
        {
            NavigateTo(typeof(T), parameter);
        }

        public void NavigateTo(Type pageType)
        {
            NavigateTo(pageType, null);
        }

        public void NavigateTo(Type pageType, object? parameter)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"NavigateTo: Starting navigation to {pageType.Name}");
                System.Console.WriteLine($"NavigateTo: Starting navigation to {pageType.Name}");

                // Create page instance
                var page = CreatePageInstance(pageType);
                if (page == null)
                {
                    System.Diagnostics.Debug.WriteLine($"NavigateTo: Failed to create page instance for {pageType.Name}");
                    System.Console.WriteLine($"NavigateTo: Failed to create page instance for {pageType.Name}");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"NavigateTo: Successfully created page instance for {pageType.Name}");
                System.Console.WriteLine($"NavigateTo: Successfully created page instance for {pageType.Name}");

                // Get page title
                var title = GetPageTitle(pageType);

                // Add to navigation stack
                _navigationStack.Push(new NavigationItem
                {
                    PageType = pageType,
                    Page = page,
                    Title = title,
                    Parameter = parameter // parameter can be null, which is acceptable
                });

                // Update current page
                CurrentPage = page;
                CurrentPageTitle = title ?? string.Empty;

                // Pass parameter if page supports it
                if (parameter != null && page is INavigationAware navigationAware)
                {
                    System.Console.WriteLine($"NavigateTo: Calling OnNavigatedTo for {pageType.Name}");
                    navigationAware.OnNavigatedTo(parameter);
                }

                // Raise navigation event on UI thread
                System.Diagnostics.Debug.WriteLine($"NavigateTo: Raising navigation event for {pageType.Name}");
                System.Console.WriteLine($"NavigateTo: Raising navigation event for {pageType.Name}");
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"NavigateTo: Invoking Navigated event for {pageType.Name}");
                    System.Console.WriteLine($"NavigateTo: Invoking Navigated event for {pageType.Name}");
                    Navigated?.Invoke(this, new NavigationEventArgs
                    {
                        Page = page,
                        Title = title ?? string.Empty,
                        Parameter = parameter
                    });
                    System.Diagnostics.Debug.WriteLine($"NavigateTo: Navigation completed for {pageType.Name}");
                    System.Console.WriteLine($"NavigateTo: Navigation completed for {pageType.Name}");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"NavigateTo: Navigation error for {pageType.Name}: {ex.Message}");
                System.Console.WriteLine($"NavigateTo: Navigation error for {pageType.Name}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"NavigateTo: Stack trace: {ex.StackTrace}");
                System.Console.WriteLine($"NavigateTo: Stack trace: {ex.StackTrace}");
            }
        }

        public void GoBack()
        {
            if (!CanGoBack) return;

            // Remove current page
            _navigationStack.Pop();

            // Get previous page
            var previousItem = _navigationStack.Peek();
            CurrentPage = previousItem.Page;
            CurrentPageTitle = previousItem.Title;

            // Raise navigation event on UI thread
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                Navigated?.Invoke(this, new NavigationEventArgs
                {
                    Page = CurrentPage,
                    Title = CurrentPageTitle,
                    Parameter = previousItem.Parameter
                });
            });
        }

        private UserControl? CreatePageInstance(Type pageType)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Attempting to create {pageType.Name}");
                System.Console.WriteLine($"CreatePageInstance: Attempting to create {pageType.Name}");

                // Try to get from DI container first
                var pageInstance = _serviceProvider.GetService(pageType);
                if (pageInstance != null)
                {
                    // Check if it's UserControl first, then Page
                    if (pageInstance is UserControl userControl)
                    {
                        System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Successfully created {pageType.Name} from DI as UserControl");
                        System.Console.WriteLine($"CreatePageInstance: Successfully created {pageType.Name} from DI as UserControl");
                        return userControl;
                    }
                    else if (pageInstance is Page page)
                    {
                        System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Created {pageType.Name} as Page but need UserControl");
                        System.Console.WriteLine($"CreatePageInstance: Created {pageType.Name} as Page but need UserControl");
                        // Cannot convert Page to UserControl, return null
                        return null;
                    }
                }

                System.Diagnostics.Debug.WriteLine($"CreatePageInstance: DI failed for {pageType.Name}, trying Activator");
                System.Console.WriteLine($"CreatePageInstance: DI failed for {pageType.Name}, trying Activator");

                // Fallback to Activator
                var activatorInstance = Activator.CreateInstance(pageType);
                if (activatorInstance is UserControl userControlFromActivator)
                {
                    System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Successfully created {pageType.Name} from Activator as UserControl");
                    System.Console.WriteLine($"CreatePageInstance: Successfully created {pageType.Name} from Activator as UserControl");
                    return userControlFromActivator;
                }
                else if (activatorInstance is Page pageFromActivator)
                {
                    System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Created {pageType.Name} as Page but need UserControl");
                    System.Console.WriteLine($"CreatePageInstance: Created {pageType.Name} as Page but need UserControl");
                    // Cannot convert Page to UserControl, return null
                    return null;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Activator failed for {pageType.Name}");
                    System.Console.WriteLine($"CreatePageInstance: Activator failed for {pageType.Name}");
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Exception creating {pageType.Name}: {ex.Message}");
                System.Console.WriteLine($"CreatePageInstance: Exception creating {pageType.Name}: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"CreatePageInstance: Stack trace: {ex.StackTrace}");
                System.Console.WriteLine($"CreatePageInstance: Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        private string GetPageTitle(Type pageType)
        {
            return pageType.Name switch
            {
                "DashboardPage" => "لوحة التحكم",
                "InvoicesPage" => "إدارة الفواتير",
                "PaymentsPage" => "إدارة المدفوعات",
                "SuppliersPage" => "إدارة الموردين",
                "OffersPage" => "عروض المندوبين",
                "SearchPage" => "البحث والتقارير",
                "ReportsPage" => "التقارير",
                "ChartsPage" => "المخططات البيانية",
                "SettingsPage" => "الإعدادات",
                _ => "صفحة غير معروفة"
            };
        }

        private class NavigationItem
        {
            public Type PageType { get; set; } = null!;
            public UserControl Page { get; set; } = null!;
            public string Title { get; set; } = string.Empty;
            public object? Parameter { get; set; } // Parameter can be null
        }
    }

    public interface INavigationAware
    {
        void OnNavigatedTo(object parameter);
        void OnNavigatedFrom();
    }
}
