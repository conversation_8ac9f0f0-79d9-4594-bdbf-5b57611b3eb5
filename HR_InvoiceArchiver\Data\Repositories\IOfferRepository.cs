using HR_InvoiceArchiver.Models;

namespace HR_InvoiceArchiver.Data.Repositories
{
    /// <summary>
    /// واجهة مستودع العروض - نسخة مبسطة
    /// </summary>
    public interface IOfferRepository
    {
        // العمليات الأساسية
        Task<IEnumerable<Offer>> GetAllAsync();
        Task<IEnumerable<Offer>> GetActiveOffersAsync();
        Task<Offer?> GetByIdAsync(int id);
        Task<Offer> CreateAsync(Offer offer);
        Task<Offer> UpdateAsync(Offer offer);
        Task<bool> DeleteAsync(int id);
        Task<bool> SoftDeleteAsync(int id);
        Task<bool> ExistsAsync(int id);

        // البحث الأساسي
        Task<IEnumerable<Offer>> SearchAsync(string searchTerm);
        Task<IEnumerable<Offer>> GetOffersByScientificNameAsync(string scientificName);

        // إحصائيات أساسية
        Task<int> GetTotalOffersCountAsync();
        Task<int> GetActiveOffersCountAsync();
    }
}
